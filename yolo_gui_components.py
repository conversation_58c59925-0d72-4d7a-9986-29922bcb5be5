#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO GUI组件
为主GUI提供YOLO训练和识别功能的界面组件
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import json
from typing import Dict, Optional, Callable
from yolo_manager import YOLOManager

class YOLOModelSelector(ttk.Frame):
    """YOLO模型选择器"""

    def __init__(self, parent, yolo_manager: YOLOManager, **kwargs):
        super().__init__(parent, **kwargs)
        self.yolo_manager = yolo_manager
        self.selected_model = tk.StringVar()
        self.download_progress = tk.DoubleVar()

        self.create_widgets()
        self.refresh_models()

    def create_widgets(self):
        """创建界面组件"""
        # 模型选择框架
        model_frame = ttk.LabelFrame(self, text="选择YOLO模型")
        model_frame.pack(fill=tk.X, padx=5, pady=5)

        # 模型下拉列表
        self.model_combo = ttk.Combobox(model_frame, textvariable=self.selected_model,
                                       state="readonly", width=30)
        self.model_combo.pack(side=tk.LEFT, padx=5, pady=5)
        self.model_combo.bind('<<ComboboxSelected>>', self.on_model_selected)

        # 刷新按钮
        ttk.Button(model_frame, text="刷新", command=self.refresh_models).pack(side=tk.LEFT, padx=5)

        # 下载按钮
        self.download_btn = ttk.Button(model_frame, text="下载模型", command=self.download_model)
        self.download_btn.pack(side=tk.LEFT, padx=5)

        # 模型信息框架
        info_frame = ttk.LabelFrame(self, text="模型信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)

        # 模型信息文本
        self.info_text = tk.Text(info_frame, height=4, wrap=tk.WORD)
        self.info_text.pack(fill=tk.X, padx=5, pady=5)

        # 下载进度条
        self.progress_bar = ttk.Progressbar(self, variable=self.download_progress,
                                          maximum=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)
        self.progress_bar.pack_forget()  # 初始隐藏

        # 状态标签
        self.status_label = ttk.Label(self, text="")
        self.status_label.pack(pady=2)

    def refresh_models(self):
        """刷新模型列表"""
        models = self.yolo_manager.get_available_models()
        model_options = []

        for model_id, info in models.items():
            status = "✅" if info['downloaded'] else "⬇️"
            option = f"{status} {model_id} - {info['name']} ({info['size']})"
            model_options.append(option)

        self.model_combo['values'] = model_options
        if model_options and not self.selected_model.get():
            self.model_combo.current(0)
            self.on_model_selected()

    def on_model_selected(self, event=None):
        """模型选择事件"""
        selection = self.selected_model.get()
        if not selection:
            return

        # 提取模型ID
        model_id = selection.split(' - ')[0].split(' ', 1)[1]
        models = self.yolo_manager.get_available_models()

        if model_id in models:
            model_info = models[model_id]

            # 更新信息显示
            info_text = f"名称: {model_info['name']}\n"
            info_text += f"描述: {model_info['description']}\n"
            info_text += f"大小: {model_info['size']} | 速度: {model_info['speed']} | 精度: {model_info['accuracy']}\n"
            info_text += f"状态: {'已下载' if model_info['downloaded'] else '需要下载'}"

            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info_text)

            # 更新下载按钮状态
            if model_info['downloaded']:
                self.download_btn.config(text="已下载", state="disabled")
            else:
                self.download_btn.config(text="下载模型", state="normal")

    def download_model(self):
        """下载选中的模型"""
        selection = self.selected_model.get()
        if not selection:
            return

        model_id = selection.split(' - ')[0].split(' ', 1)[1]

        # 显示进度条
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)
        self.download_btn.config(state="disabled")
        self.status_label.config(text="正在下载...")

        def progress_callback(downloaded, total, percentage):
            self.download_progress.set(percentage)
            self.status_label.config(text=f"下载中... {percentage:.1f}%")

        def download_thread():
            success = self.yolo_manager.download_model(model_id, progress_callback)

            # 更新UI
            self.after(0, lambda: self.download_complete(success))

        threading.Thread(target=download_thread, daemon=True).start()

    def download_complete(self, success: bool):
        """下载完成回调"""
        self.progress_bar.pack_forget()

        if success:
            self.status_label.config(text="下载完成！")
            self.refresh_models()
            self.on_model_selected()
        else:
            self.status_label.config(text="下载失败！")
            self.download_btn.config(state="normal")

        # 3秒后清除状态
        self.after(3000, lambda: self.status_label.config(text=""))

    def get_selected_model_id(self) -> Optional[str]:
        """获取选中的模型ID"""
        selection = self.selected_model.get()
        if not selection:
            return None

        model_id = selection.split(' - ')[0].split(' ', 1)[1]
        models = self.yolo_manager.get_available_models()

        if model_id in models and models[model_id]['downloaded']:
            return model_id
        return None

    def get_selected_model_path(self) -> Optional[str]:
        """获取选中模型的路径"""
        model_id = self.get_selected_model_id()
        if model_id:
            models = self.yolo_manager.get_available_models()
            return models[model_id]['local_path']
        return None

class YOLOTrainingPanel(ttk.Frame):
    """YOLO训练面板"""

    def __init__(self, parent, yolo_manager: YOLOManager, **kwargs):
        super().__init__(parent, **kwargs)
        self.yolo_manager = yolo_manager
        self.dataset_path = tk.StringVar()
        self.epochs = tk.IntVar(value=100)
        self.batch_size = tk.IntVar(value=16)
        self.img_size = tk.IntVar(value=640)
        self.training_progress = tk.DoubleVar()

        self.create_widgets()

    def create_widgets(self):
        """创建训练界面组件"""
        # 数据集选择
        dataset_frame = ttk.LabelFrame(self, text="训练数据集")
        dataset_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Entry(dataset_frame, textvariable=self.dataset_path, width=50).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(dataset_frame, text="选择数据集", command=self.select_dataset).pack(side=tk.LEFT, padx=5)

        # 训练参数
        params_frame = ttk.LabelFrame(self, text="训练参数")
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        # 参数网格
        param_grid = ttk.Frame(params_frame)
        param_grid.pack(fill=tk.X, padx=5, pady=5)

        # 训练轮数
        ttk.Label(param_grid, text="训练轮数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(param_grid, from_=10, to=1000, textvariable=self.epochs, width=10).grid(row=0, column=1, padx=5, pady=2)

        # 批次大小
        ttk.Label(param_grid, text="批次大小:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(param_grid, from_=1, to=64, textvariable=self.batch_size, width=10).grid(row=0, column=3, padx=5, pady=2)

        # 图像尺寸
        ttk.Label(param_grid, text="图像尺寸:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(param_grid, from_=320, to=1280, increment=32, textvariable=self.img_size, width=10).grid(row=1, column=1, padx=5, pady=2)

        # 训练控制
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        self.train_btn = ttk.Button(control_frame, text="开始训练", command=lambda: self.start_training(getattr(self, 'model_selector', None)))
        self.train_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止训练", command=self.stop_training, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 训练进度
        progress_frame = ttk.LabelFrame(self, text="训练进度")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.training_progress,
                                          maximum=100, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # 训练日志
        log_frame = ttk.LabelFrame(self, text="训练日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def select_dataset(self):
        """选择数据集文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据集配置文件",
            filetypes=[("YAML files", "*.yaml"), ("All files", "*.*")]
        )
        if file_path:
            self.dataset_path.set(file_path)

    def start_training(self, model_selector=None):
        """开始训练"""
        if model_selector is None:
            # 尝试从父窗口获取模型选择器
            try:
                parent_window = self.winfo_toplevel()
                if hasattr(parent_window, 'yolo_model_selector'):
                    model_selector = parent_window.yolo_model_selector
                else:
                    messagebox.showerror("错误", "无法找到模型选择器")
                    return
            except:
                messagebox.showerror("错误", "无法找到模型选择器")
                return
        model_id = model_selector.get_selected_model_id()
        if not model_id:
            messagebox.showerror("错误", "请先选择并下载一个模型")
            return

        dataset_file = self.dataset_path.get()
        if not dataset_file or not os.path.exists(dataset_file):
            messagebox.showerror("错误", "请选择有效的数据集配置文件")
            return

        # 更新UI状态
        self.train_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.progress_bar.config(mode='indeterminate')
        self.progress_bar.start()

        self.log_message("开始训练...")
        self.log_message(f"模型: {model_id}")
        self.log_message(f"数据集: {dataset_file}")
        self.log_message(f"参数: epochs={self.epochs.get()}, batch={self.batch_size.get()}, imgsz={self.img_size.get()}")

        def training_thread():
            try:
                trained_model_path = self.yolo_manager.train_model(
                    model_id=model_id,
                    dataset_yaml=dataset_file,
                    epochs=self.epochs.get(),
                    batch_size=self.batch_size.get(),
                    img_size=self.img_size.get()
                )

                self.after(0, lambda: self.training_complete(trained_model_path))

            except Exception as e:
                self.after(0, lambda: self.training_error(str(e)))

        threading.Thread(target=training_thread, daemon=True).start()

    def stop_training(self):
        """停止训练"""
        # 注意：实际停止训练需要更复杂的实现
        self.log_message("请求停止训练...")
        self.training_complete(None)

    def training_complete(self, model_path: Optional[str]):
        """训练完成"""
        self.train_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_bar.stop()
        self.progress_bar.config(mode='determinate')

        if model_path:
            self.log_message(f"训练完成！模型保存至: {model_path}")
            messagebox.showinfo("训练完成", f"模型训练完成！\n保存路径: {model_path}")
        else:
            self.log_message("训练被中断或失败")

    def training_error(self, error_msg: str):
        """训练错误"""
        self.train_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_bar.stop()

        self.log_message(f"训练错误: {error_msg}")
        messagebox.showerror("训练错误", f"训练过程中发生错误:\n{error_msg}")

    def log_message(self, message: str):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.update_idletasks()

class YOLOPredictionPanel(ttk.Frame):
    """YOLO预测面板"""

    def __init__(self, parent, yolo_manager: YOLOManager, **kwargs):
        super().__init__(parent, **kwargs)
        self.yolo_manager = yolo_manager
        self.image_path = tk.StringVar()
        self.confidence = tk.DoubleVar(value=0.5)
        self.save_results = tk.BooleanVar(value=True)

        self.create_widgets()

    def create_widgets(self):
        """创建预测界面组件"""
        # 图像选择
        image_frame = ttk.LabelFrame(self, text="选择图像")
        image_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Entry(image_frame, textvariable=self.image_path, width=50).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(image_frame, text="选择图像", command=self.select_image).pack(side=tk.LEFT, padx=5)

        # 预测参数
        params_frame = ttk.LabelFrame(self, text="预测参数")
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        # 置信度
        conf_frame = ttk.Frame(params_frame)
        conf_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(conf_frame, text="置信度阈值:").pack(side=tk.LEFT)
        ttk.Scale(conf_frame, from_=0.1, to=1.0, variable=self.confidence,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, padx=5)
        ttk.Label(conf_frame, textvariable=self.confidence).pack(side=tk.LEFT)

        # 保存结果选项
        ttk.Checkbutton(params_frame, text="保存预测结果",
                       variable=self.save_results).pack(anchor=tk.W, padx=5, pady=2)

        # 预测按钮
        self.predict_btn = ttk.Button(self, text="开始预测", command=lambda: self.start_prediction(getattr(self, 'model_selector', None)))
        self.predict_btn.pack(pady=10)

        # 结果显示
        results_frame = ttk.LabelFrame(self, text="预测结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.results_text = tk.Text(results_frame, height=10, wrap=tk.WORD)
        results_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)

        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def select_image(self):
        """选择图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.image_path.set(file_path)

    def start_prediction(self, model_selector=None):
        """开始预测"""
        if model_selector is None:
            # 尝试从父窗口获取模型选择器
            try:
                parent_window = self.winfo_toplevel()
                if hasattr(parent_window, 'yolo_model_selector'):
                    model_selector = parent_window.yolo_model_selector
                else:
                    messagebox.showerror("错误", "无法找到模型选择器")
                    return
            except:
                messagebox.showerror("错误", "无法找到模型选择器")
                return
        model_path = model_selector.get_selected_model_path()
        if not model_path:
            messagebox.showerror("错误", "请先选择并下载一个模型")
            return

        image_file = self.image_path.get()
        if not image_file or not os.path.exists(image_file):
            messagebox.showerror("错误", "请选择有效的图像文件")
            return

        self.predict_btn.config(state="disabled")
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "正在预测...\n")

        def prediction_thread():
            try:
                save_dir = "yolo_predictions" if self.save_results.get() else None

                results = self.yolo_manager.predict_image(
                    model_path=model_path,
                    image_path=image_file,
                    confidence=self.confidence.get(),
                    save_dir=save_dir
                )

                self.after(0, lambda: self.prediction_complete(results))

            except Exception as e:
                self.after(0, lambda: self.prediction_error(str(e)))

        threading.Thread(target=prediction_thread, daemon=True).start()

    def prediction_complete(self, results: Optional[Dict]):
        """预测完成"""
        self.predict_btn.config(state="normal")

        if results:
            self.display_results(results)
        else:
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, "预测失败或无结果\n")

    def prediction_error(self, error_msg: str):
        """预测错误"""
        self.predict_btn.config(state="normal")
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"预测错误: {error_msg}\n")

    def display_results(self, results: Dict):
        """显示预测结果"""
        self.results_text.delete(1.0, tk.END)

        result_text = f"预测完成！\n"
        result_text += f"图像: {os.path.basename(results['image_path'])}\n"
        result_text += f"检测到 {results['total_detections']} 个对象\n\n"

        if results['detections']:
            result_text += "检测详情:\n"
            for i, detection in enumerate(results['detections'], 1):
                result_text += f"{i}. {detection['class_name']}\n"
                result_text += f"   置信度: {detection['confidence']:.3f}\n"
                result_text += f"   位置: ({detection['bbox'][0]:.1f}, {detection['bbox'][1]:.1f}, "
                result_text += f"{detection['bbox'][2]:.1f}, {detection['bbox'][3]:.1f})\n"
                result_text += f"   面积: {detection['area']:.1f} 像素\n\n"

        if self.save_results.get():
            result_text += "结果已保存到 yolo_predictions/ 目录\n"

        self.results_text.insert(1.0, result_text)