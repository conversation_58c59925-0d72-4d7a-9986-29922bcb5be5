#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复YOLO数据集问题
解决数据集创建和YAML文件生成问题
"""

import os
import sys
import json
import shutil
from pathlib import Path

def find_segmentation_results(base_dir):
    """查找分割结果目录"""
    print(f"🔍 在 {base_dir} 中查找分割结果...")
    
    results = []
    
    # 查找包含YOLO JSON文件的目录
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('_yolo_annotations.json'):
                json_path = os.path.join(root, file)
                
                # 查找对应的原始图像
                base_name = file.replace('_yolo_annotations.json', '')
                
                # 查找原始图像文件
                image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
                original_image = None
                
                for ext in image_extensions:
                    potential_image = os.path.join(root, f"{base_name}{ext}")
                    if os.path.exists(potential_image):
                        original_image = potential_image
                        break
                
                if original_image:
                    results.append({
                        'json_path': json_path,
                        'image_path': original_image,
                        'base_name': base_name,
                        'directory': root
                    })
                    print(f"  ✅ 找到: {base_name}")
    
    print(f"📊 总共找到 {len(results)} 个分割结果")
    return results

def create_yolo_dataset_from_results(results, output_dir, train_ratio=0.8):
    """从分割结果创建YOLO数据集"""
    print(f"\n📦 创建YOLO数据集到: {output_dir}")
    
    if not results:
        print("❌ 没有找到分割结果")
        return None
    
    # 创建数据集目录结构
    dataset_dirs = {
        'images': {
            'train': os.path.join(output_dir, 'images', 'train'),
            'val': os.path.join(output_dir, 'images', 'val')
        },
        'labels': {
            'train': os.path.join(output_dir, 'labels', 'train'),
            'val': os.path.join(output_dir, 'labels', 'val')
        }
    }
    
    # 创建目录
    for category in dataset_dirs.values():
        for split_dir in category.values():
            os.makedirs(split_dir, exist_ok=True)
    
    # 分割训练集和验证集
    import random
    random.shuffle(results)
    split_idx = int(len(results) * train_ratio)
    train_results = results[:split_idx]
    val_results = results[split_idx:]
    
    print(f"📊 数据分割: 训练集 {len(train_results)} 张, 验证集 {len(val_results)} 张")
    
    # 处理训练集
    train_count = process_dataset_split(train_results, dataset_dirs, 'train')
    
    # 处理验证集
    val_count = process_dataset_split(val_results, dataset_dirs, 'val')
    
    # 创建数据集配置文件
    dataset_yaml = create_dataset_yaml(output_dir, train_count, val_count)
    
    # 创建类别文件
    create_classes_file(output_dir)
    
    dataset_info = {
        'dataset_dir': output_dir,
        'train_images': train_count,
        'val_images': val_count,
        'total_images': train_count + val_count,
        'classes': ['seed'],
        'config_file': dataset_yaml
    }
    
    print(f"✅ YOLO数据集创建完成!")
    print(f"   配置文件: {dataset_yaml}")
    print(f"   训练图像: {train_count}")
    print(f"   验证图像: {val_count}")
    
    return dataset_info

def process_dataset_split(results, dataset_dirs, split):
    """处理数据集分割"""
    count = 0
    
    for result in results:
        try:
            # 复制图像文件
            image_filename = os.path.basename(result['image_path'])
            dest_image_path = os.path.join(dataset_dirs['images'][split], image_filename)
            shutil.copy2(result['image_path'], dest_image_path)
            
            # 转换JSON标注为YOLO txt格式
            base_name = os.path.splitext(image_filename)[0]
            txt_filename = f"{base_name}.txt"
            dest_label_path = os.path.join(dataset_dirs['labels'][split], txt_filename)
            
            if convert_json_to_yolo_txt(result['json_path'], dest_label_path):
                count += 1
                print(f"  ✅ 处理: {image_filename}")
            else:
                print(f"  ❌ 转换失败: {image_filename}")
                
        except Exception as e:
            print(f"  ❌ 处理文件失败 {result['base_name']}: {e}")
    
    return count

def convert_json_to_yolo_txt(json_path, output_txt_path):
    """将JSON标注转换为YOLO txt格式"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        image_width = data['imageWidth']
        image_height = data['imageHeight']
        
        with open(output_txt_path, 'w') as f:
            for shape in data['shapes']:
                if shape['shape_type'] == 'polygon' and shape['label'] == 'seed':
                    # 获取边界框坐标
                    points = shape['points']
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    
                    x_min, x_max = min(x_coords), max(x_coords)
                    y_min, y_max = min(y_coords), max(y_coords)
                    
                    # 转换为YOLO格式 (归一化的中心点坐标和宽高)
                    center_x = (x_min + x_max) / 2.0 / image_width
                    center_y = (y_min + y_max) / 2.0 / image_height
                    width = (x_max - x_min) / image_width
                    height = (y_max - y_min) / image_height
                    
                    # 写入YOLO格式: class_id center_x center_y width height
                    f.write(f"0 {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}\n")
        
        return True
        
    except Exception as e:
        print(f"转换JSON到YOLO格式失败: {e}")
        return False

def create_dataset_yaml(output_dir, train_count, val_count):
    """创建数据集YAML配置文件"""
    yaml_content = f"""# YOLO数据集配置文件
# 由透明背景种子分割系统自动生成

# 数据集路径
path: {os.path.abspath(output_dir)}
train: images/train
val: images/val

# 类别数量
nc: 1

# 类别名称
names: ['seed']

# 数据集统计
train_images: {train_count}
val_images: {val_count}
total_images: {train_count + val_count}

# 生成信息
generated_by: "Transparent Seed Segmentation System"
description: "种子检测数据集，用于YOLO训练"
"""
    
    yaml_path = os.path.join(output_dir, 'dataset.yaml')
    with open(yaml_path, 'w', encoding='utf-8') as f:
        f.write(yaml_content)
    
    return yaml_path

def create_classes_file(output_dir):
    """创建类别文件"""
    classes_path = os.path.join(output_dir, 'classes.txt')
    with open(classes_path, 'w', encoding='utf-8') as f:
        f.write("seed\n")

def auto_create_dataset_from_output(output_base_dir):
    """自动从输出目录创建数据集"""
    print(f"🤖 自动创建YOLO数据集")
    print("=" * 50)
    
    # 查找分割结果
    results = find_segmentation_results(output_base_dir)
    
    if not results:
        print(f"❌ 在 {output_base_dir} 中没有找到分割结果")
        print(f"请确保:")
        print(f"1. 已经进行了图像分割")
        print(f"2. 启用了'生成YOLO标注文件'选项")
        print(f"3. 分割结果保存在指定目录中")
        return None
    
    # 创建数据集目录
    dataset_dir = os.path.join(output_base_dir, "yolo_dataset")
    
    # 创建数据集
    dataset_info = create_yolo_dataset_from_results(results, dataset_dir)
    
    return dataset_info

def main():
    """主函数"""
    print("🔧 YOLO数据集问题修复工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        output_dir = sys.argv[1]
    else:
        # 默认查找常见的输出目录
        possible_dirs = [
            "output",
            "results", 
            "segmentation_results",
            "yolo_demo_results",
            "."
        ]
        
        output_dir = None
        for dir_name in possible_dirs:
            if os.path.exists(dir_name):
                output_dir = dir_name
                break
        
        if not output_dir:
            print("❌ 未找到输出目录")
            print("用法: python fix_yolo_dataset_issues.py [输出目录路径]")
            return 1
    
    print(f"📁 检查目录: {os.path.abspath(output_dir)}")
    
    # 自动创建数据集
    dataset_info = auto_create_dataset_from_output(output_dir)
    
    if dataset_info:
        print(f"\n🎉 数据集创建成功!")
        print(f"📋 使用说明:")
        print(f"1. 在GUI的训练页面中")
        print(f"2. 选择数据集文件: {dataset_info['config_file']}")
        print(f"3. 或者点击'使用最新分割数据集'按钮")
        print(f"4. 开始训练您的YOLO模型")
        
        return 0
    else:
        print(f"\n❌ 数据集创建失败")
        print(f"💡 解决建议:")
        print(f"1. 确保已经进行了图像分割")
        print(f"2. 在分割时启用'生成YOLO标注文件'")
        print(f"3. 检查输出目录是否包含 *_yolo_annotations.json 文件")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
