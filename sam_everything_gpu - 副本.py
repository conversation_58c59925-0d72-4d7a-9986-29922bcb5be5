#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU-accelerated SAM Everything with YOLO Label Generation
- GPU/CPU device selection support
- CUDA 11.2 compatibility
- Official SAM demo parameters
- YOLO format label generation
- Enhanced performance and functionality
"""

import os
import cv2
import numpy as np
import time
import logging
import argparse
from typing import Dict, List, Tuple, Optional
from PIL import Image

class GPUAcceleratedSAMEverything:
    """GPU-accelerated SAM Everything with YOLO label generation"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.sam_generator = None
        self.device = config.get('device', 'auto')
        self.logger = logging.getLogger(__name__)
        self._initialize()
    
    def safe_imread(self, image_path: str) -> Optional[np.ndarray]:
        """安全读取图像，支持中文路径"""
        try:
            # 方法1: 直接使用OpenCV（可能失败）
            image = cv2.imread(image_path)
            if image is not None:
                return image
            
            # 方法2: 使用numpy和cv2.imdecode
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)
            
            # 使用cv2.imdecode解码
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is not None:
                return image
            
            # 方法3: 使用PIL然后转换为OpenCV格式
            pil_image = Image.open(image_path)
            
            # 转换为RGB（PIL默认是RGB，OpenCV是BGR）
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # 转换为numpy数组
            image_array = np.array(pil_image)
            
            # 转换为BGR格式（OpenCV格式）
            image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            return image_bgr
            
        except Exception as e:
            self.logger.error(f"读取图像失败 {image_path}: {e}")
            return None
    
    def safe_imwrite(self, image_path: str, image: np.ndarray) -> bool:
        """安全保存图像，支持中文路径"""
        try:
            # 方法1: 直接使用OpenCV（可能失败）
            success = cv2.imwrite(image_path, image)
            if success:
                return True
            
            # 方法2: 使用cv2.imencode然后写入文件
            # 获取文件扩展名
            ext = os.path.splitext(image_path)[1].lower()
            if ext in ['.jpg', '.jpeg']:
                encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 95]
                success, encoded_img = cv2.imencode('.jpg', image, encode_param)
            elif ext == '.png':
                encode_param = [int(cv2.IMWRITE_PNG_COMPRESSION), 3]
                success, encoded_img = cv2.imencode('.png', image, encode_param)
            else:
                success, encoded_img = cv2.imencode('.jpg', image)
            
            if success:
                with open(image_path, 'wb') as f:
                    f.write(encoded_img.tobytes())
                return True
            
            # 方法3: 使用PIL保存
            # 转换为RGB格式
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            pil_image.save(image_path)
            return True
            
        except Exception as e:
            self.logger.error(f"保存图像失败 {image_path}: {e}")
            return False
    
    def _determine_device(self) -> str:
        """确定使用的设备"""
        try:
            import torch
            
            if self.device == 'auto':
                if torch.cuda.is_available():
                    device = 'cuda'
                    self.logger.info(f"自动选择GPU设备: {torch.cuda.get_device_name()}")
                else:
                    device = 'cpu'
                    self.logger.info("GPU不可用，使用CPU设备")
            elif self.device == 'gpu' or self.device == 'cuda':
                if torch.cuda.is_available():
                    device = 'cuda'
                    self.logger.info(f"强制使用GPU设备: {torch.cuda.get_device_name()}")
                else:
                    self.logger.warning("请求使用GPU但CUDA不可用，回退到CPU")
                    device = 'cpu'
            else:
                device = 'cpu'
                self.logger.info("使用CPU设备")
            
            return device
            
        except ImportError:
            self.logger.warning("PyTorch未安装，使用CPU设备")
            return 'cpu'
    
    def _initialize(self):
        """初始化SAM Everything"""
        try:
            from segment_anything import sam_model_registry, SamAutomaticMaskGenerator
            import torch
            
            # 检查模型文件
            model_path = self.config.get('checkpoint_path', 'sam_vit_h_4b8939.pth')
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"SAM模型文件不存在: {model_path}")
            
            # 确定设备
            device = self._determine_device()
            
            # 加载模型
            sam = sam_model_registry["vit_h"](checkpoint=model_path)
            sam.to(device=device)
            
            # 使用官方SAM demo网站的默认参数
            # 参考: https://segment-anything.com/demo
            self.sam_generator = SamAutomaticMaskGenerator(
                model=sam,
                points_per_side=self.config.get('points_per_side', 32),
                pred_iou_thresh=self.config.get('pred_iou_thresh', 0.88),
                stability_score_thresh=self.config.get('stability_score_thresh', 0.95),
                crop_n_layers=self.config.get('crop_n_layers', 1),
                crop_n_points_downscale_factor=self.config.get('crop_n_points_downscale_factor', 2),
                min_mask_region_area=self.config.get('min_mask_region_area', 100),
                box_nms_thresh=self.config.get('box_nms_thresh', 0.7),
                crop_overlap_ratio=self.config.get('crop_overlap_ratio', 512 / 1500),
                crop_nms_thresh=self.config.get('crop_nms_thresh', 0.7),
            )
            
            self.logger.info(f"GPU-accelerated SAM Everything初始化成功 (设备: {device})")
            
        except ImportError:
            self.logger.error("SAM不可用，请检查segment-anything安装")
            self.sam_generator = None
        except Exception as e:
            self.logger.error(f"SAM Everything初始化失败: {e}")
            self.sam_generator = None
    
    def mask_to_yolo_bbox(self, mask: Dict, image_shape: Tuple[int, int]) -> Tuple[float, float, float, float]:
        """将SAM掩膜转换为YOLO格式的边界框"""
        # 获取边界框
        bbox = mask['bbox']  # [x, y, width, height]
        x, y, w, h = bbox
        
        # 图像尺寸
        img_height, img_width = image_shape[:2]
        
        # 计算中心点坐标
        center_x = x + w / 2
        center_y = y + h / 2
        
        # 归一化到[0, 1]
        center_x_norm = center_x / img_width
        center_y_norm = center_y / img_height
        width_norm = w / img_width
        height_norm = h / img_height
        
        return center_x_norm, center_y_norm, width_norm, height_norm
    
    def generate_yolo_labels(self, masks: List[Dict], image_shape: Tuple[int, int], 
                           output_dir: str, image_name: str) -> str:
        """生成YOLO格式的标签文件"""
        try:
            # 创建yolo_label目录
            yolo_label_dir = os.path.join(output_dir, 'yolo_label')
            os.makedirs(yolo_label_dir, exist_ok=True)
            
            # 生成标签文件名
            base_name = os.path.splitext(image_name)[0]
            label_file = os.path.join(yolo_label_dir, f"{base_name}.txt")
            
            # 写入YOLO格式标签
            with open(label_file, 'w', encoding='utf-8') as f:
                for i, mask in enumerate(masks):
                    # 转换为YOLO格式
                    center_x, center_y, width, height = self.mask_to_yolo_bbox(mask, image_shape)
                    
                    # 类别ID（这里使用0作为默认类别，可以根据需要修改）
                    class_id = self.config.get('default_class_id', 0)
                    
                    # 写入标签行: class_id center_x center_y width height
                    f.write(f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}\n")
            
            self.logger.info(f"YOLO标签文件已生成: {label_file}")
            return label_file
            
        except Exception as e:
            self.logger.error(f"生成YOLO标签失败: {e}")
            return None
    
    def filter_whole_image_masks(self, masks: List[Dict], image_shape: Tuple[int, int],
                                max_mask_ratio: float = 0.8) -> Tuple[List[Dict], int]:
        """过滤覆盖整个图像的掩膜"""
        if len(masks) == 0:
            return masks, 0

        # 计算图像总面积
        total_image_area = image_shape[0] * image_shape[1]

        filtered_masks = []
        removed_count = 0

        for mask in masks:
            mask_area = mask['area']
            area_ratio = mask_area / total_image_area

            # 如果掩膜覆盖面积超过阈值，则过滤掉
            if area_ratio > max_mask_ratio:
                removed_count += 1
                self.logger.debug(f"丢弃全图掩膜: 面积比例 {area_ratio:.3f} > {max_mask_ratio}")
            else:
                filtered_masks.append(mask)

        if removed_count > 0:
            self.logger.info(f"全图掩膜过滤: 移除 {removed_count} 个全图掩膜 (面积比例 > {max_mask_ratio:.1%})")

        return filtered_masks, removed_count

    def filter_overlapping_masks(self, masks: List[Dict], overlap_threshold: float = 0.1) -> List[Dict]:
        """过滤重叠的掩膜，移除冗余的小分割"""
        if len(masks) <= 1:
            return masks

        # 按面积排序（大到小），优先保留大的掩膜
        sorted_masks = sorted(masks, key=lambda x: x['area'], reverse=True)
        filtered_masks = []

        for i, current_mask in enumerate(sorted_masks):
            should_keep = True
            current_segmentation = current_mask['segmentation']
            current_area = current_mask['area']

            # 检查与已保留的掩膜是否重叠
            for kept_mask in filtered_masks:
                kept_segmentation = kept_mask['segmentation']
                kept_area = kept_mask['area']

                # 计算交集
                intersection = np.logical_and(current_segmentation, kept_segmentation)
                intersection_area = np.sum(intersection)

                # 计算重叠比例（相对于较小掩膜的面积）
                smaller_area = min(current_area, kept_area)
                overlap_ratio = intersection_area / smaller_area if smaller_area > 0 else 0

                # 如果重叠比例超过阈值，丢弃当前掩膜（因为已保留的掩膜更大）
                if overlap_ratio > overlap_threshold:
                    should_keep = False
                    self.logger.debug(f"丢弃重叠掩膜: 重叠比例 {overlap_ratio:.3f} > {overlap_threshold}")
                    break

            if should_keep:
                filtered_masks.append(current_mask)

        overlap_removed = len(masks) - len(filtered_masks)
        if overlap_removed > 0:
            self.logger.info(f"掩膜重叠过滤: 移除 {overlap_removed} 个重叠掩膜")

        return filtered_masks

    def process_image(self, image: np.ndarray, image_path: str, output_dir: str) -> Dict:
        """处理图像并保存所有检测对象，生成YOLO标签"""
        start_time = time.time()

        if self.sam_generator is None:
            return {
                'success': False,
                'error': 'SAM Everything未初始化',
                'objects_count': 0,
                'processing_time': 0,
                'output_dir': output_dir
            }

        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)

            # 使用SAM Everything进行自动分割
            self.logger.info("开始GPU-accelerated SAM Everything分割...")
            masks = self.sam_generator.generate(image)

            # 按面积排序（大到小）
            masks = sorted(masks, key=lambda x: x['area'], reverse=True)

            # 过滤覆盖整个图像的掩膜
            max_mask_ratio = self.config.get('max_mask_ratio', 0.8)
            whole_image_filtered_masks, whole_image_removed = self.filter_whole_image_masks(
                masks, image.shape, max_mask_ratio)

            # 过滤太小的对象
            min_area = self.config.get('min_object_area', 100)
            size_filtered_masks = [mask for mask in whole_image_filtered_masks if mask['area'] >= min_area]

            # 过滤重叠的掩膜
            overlap_threshold = self.config.get('overlap_threshold', 0.1)
            filtered_masks = self.filter_overlapping_masks(size_filtered_masks, overlap_threshold)

            # 生成YOLO标签文件
            yolo_label_file = None
            if self.config.get('generate_yolo_labels', True):
                image_name = os.path.basename(image_path)
                yolo_label_file = self.generate_yolo_labels(filtered_masks, image.shape, output_dir, image_name)

            # 保存所有检测对象（使用掩膜提取）
            saved_objects = []
            for i, mask in enumerate(filtered_masks):
                object_info = self._save_object_with_mask(image, mask, i + 1, output_dir)
                if object_info:
                    saved_objects.append(object_info)

            # 创建可视化结果
            result_image = self._create_visualization(image, filtered_masks)

            # 保存结果图像
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            result_path = os.path.join(output_dir, f"{base_name}_result.jpg")
            self.safe_imwrite(result_path, result_image)

            processing_time = time.time() - start_time

            return {
                'success': True,
                'objects_count': len(saved_objects),
                'processing_time': processing_time,
                'output_dir': output_dir,
                'result_image_path': result_path,
                'yolo_label_file': yolo_label_file,
                'objects': saved_objects,
                'result_image': result_image,
                'original_masks_count': len(masks),
                'whole_image_removed_count': whole_image_removed,
                'size_filtered_count': len(size_filtered_masks),
                'filtered_masks_count': len(filtered_masks)
            }

        except Exception as e:
            self.logger.error(f"图像处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'objects_count': 0,
                'processing_time': time.time() - start_time,
                'output_dir': output_dir
            }

    def _save_object_with_mask(self, image: np.ndarray, mask: Dict, object_id: int, output_dir: str) -> Optional[Dict]:
        """使用掩膜保存单个检测对象，背景透明"""
        try:
            # 获取掩膜和边界框
            segmentation = mask['segmentation']
            bbox = mask['bbox']
            x, y, w, h = bbox

            # 确保边界框在图像范围内
            x = max(0, x)
            y = max(0, y)
            w = min(w, image.shape[1] - x)
            h = min(h, image.shape[0] - y)

            if w <= 0 or h <= 0:
                return None

            # 裁剪图像和掩膜到边界框区域
            cropped_image = image[y:y+h, x:x+w]
            cropped_mask = segmentation[y:y+h, x:x+w]

            # 创建RGBA图像（包含alpha通道）
            if len(cropped_image.shape) == 3:
                # BGR转RGB
                cropped_image_rgb = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2RGB)
            else:
                cropped_image_rgb = cropped_image

            # 创建4通道图像（RGB + Alpha）
            h_crop, w_crop = cropped_image_rgb.shape[:2]
            rgba_image = np.zeros((h_crop, w_crop, 4), dtype=np.uint8)

            # 复制RGB通道
            rgba_image[:, :, :3] = cropped_image_rgb

            # 设置Alpha通道：掩膜内为255（不透明），掩膜外为0（透明）
            rgba_image[:, :, 3] = cropped_mask.astype(np.uint8) * 255

            # 保存为PNG格式以支持透明度
            object_filename = f"object_{object_id:03d}.png"
            object_path = os.path.join(output_dir, object_filename)

            # 使用PIL保存PNG图像以确保透明度正确处理
            from PIL import Image as PILImage
            pil_image = PILImage.fromarray(rgba_image, 'RGBA')
            pil_image.save(object_path, 'PNG')

            self.logger.debug(f"保存透明背景对象: {object_filename}")

            return {
                'id': object_id,
                'filename': object_filename,
                'path': object_path,
                'bbox': bbox,
                'area': mask['area'],
                'confidence': mask['stability_score'],
                'size': (w, h),
                'extraction_method': 'mask_based',
                'has_transparency': True
            }

        except Exception as e:
            self.logger.error(f"保存对象 {object_id} 失败: {e}")
            return None

    def _save_object(self, image: np.ndarray, mask: Dict, object_id: int, output_dir: str) -> Optional[Dict]:
        """保存单个检测对象（向后兼容的方法，调用新的掩膜方法）"""
        return self._save_object_with_mask(image, mask, object_id, output_dir)

    def get_official_demo_config(self) -> Dict:
        """获取官方SAM demo网站的默认配置"""
        return {
            'checkpoint_path': 'sam_vit_h_4b8939.pth',
            'device': 'auto',

            # 官方SAM demo网站的默认参数
            'points_per_side': 32,
            'pred_iou_thresh': 0.88,
            'stability_score_thresh': 0.95,
            'crop_n_layers': 1,
            'crop_n_points_downscale_factor': 2,
            'min_mask_region_area': 100,
            'box_nms_thresh': 0.7,
            'crop_overlap_ratio': 512 / 1500,
            'crop_nms_thresh': 0.7,

            # 扩展参数
            'min_object_area': 100,        # 最小对象面积
            'overlap_threshold': 0.1,      # 掩膜重叠过滤阈值
            'max_mask_ratio': 0.8,         # 最大掩膜面积比例（防止全图掩膜）
            'show_masks': True,            # 显示掩膜
            'mask_alpha': 0.35,           # 掩膜透明度
            'default_class_id': 0,        # YOLO默认类别ID
            'generate_yolo_labels': True, # 是否生成YOLO标签
            'use_mask_extraction': True,  # 使用掩膜提取（透明背景）
        }

    def _create_visualization(self, image: np.ndarray, masks: List[Dict]) -> np.ndarray:
        """创建可视化结果"""
        vis_image = image.copy()

        # 如果启用掩膜显示
        if self.config.get('show_masks', True):
            mask_alpha = self.config.get('mask_alpha', 0.35)

            # 创建掩膜叠加层
            mask_overlay = np.zeros_like(image, dtype=np.uint8)

            for i, mask in enumerate(masks):
                mask_array = mask['segmentation'].astype(bool)

                # 生成随机颜色
                np.random.seed(i)
                color = np.random.randint(0, 255, 3).tolist()

                # 应用掩膜颜色
                mask_overlay[mask_array] = color

            # 混合原图和掩膜
            vis_image = cv2.addWeighted(vis_image, 1 - mask_alpha, mask_overlay, mask_alpha, 0)

        # 添加边界框和编号
        for i, mask in enumerate(masks):
            bbox = mask['bbox']
            x, y, w, h = bbox

            # 绘制边界框
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # 添加对象编号
            label = f"#{i+1}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

            # 绘制标签背景
            cv2.rectangle(vis_image, (x, y - label_size[1] - 10),
                         (x + label_size[0] + 5, y), (0, 255, 0), -1)

            # 绘制标签文字
            cv2.putText(vis_image, label, (x + 2, y - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

        # 添加统计信息
        info_text = f"Objects: {len(masks)}"
        info_size = cv2.getTextSize(info_text, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]

        # 绘制信息背景
        cv2.rectangle(vis_image, (10, 10), (info_size[0] + 20, info_size[1] + 20), (0, 0, 0), -1)
        cv2.rectangle(vis_image, (10, 10), (info_size[0] + 20, info_size[1] + 20), (255, 255, 255), 2)

        # 绘制信息文字
        cv2.putText(vis_image, info_text, (15, info_size[1] + 15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        return vis_image

    def batch_process(self, input_dir: str, output_base_dir: str,
                     image_extensions: List[str] = None) -> Dict:
        """批量处理图像"""
        if image_extensions is None:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

        # 查找所有图像文件
        image_files = []
        for ext in image_extensions:
            pattern = os.path.join(input_dir, f"*{ext}")
            import glob
            image_files.extend(glob.glob(pattern))
            image_files.extend(glob.glob(pattern.upper()))

        if not image_files:
            return {
                'success': False,
                'error': f'在目录 {input_dir} 中未找到图像文件',
                'processed_count': 0,
                'total_objects': 0
            }

        results = []
        total_objects = 0

        for i, image_path in enumerate(image_files):
            try:
                # 使用安全读取方法加载图像
                image = self.safe_imread(image_path)
                if image is None:
                    self.logger.warning(f"无法读取图像: {image_path}")
                    continue

                # 创建输出目录
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_dir = os.path.join(output_base_dir, base_name)

                # 处理图像
                result = self.process_image(image, image_path, output_dir)
                results.append(result)

                if result['success']:
                    total_objects += result['objects_count']

                self.logger.info(f"处理完成 {i+1}/{len(image_files)}: {base_name}")

            except Exception as e:
                self.logger.error(f"处理图像 {image_path} 失败: {e}")

        return {
            'success': True,
            'processed_count': len([r for r in results if r['success']]),
            'total_files': len(image_files),
            'total_objects': total_objects,
            'results': results
        }
