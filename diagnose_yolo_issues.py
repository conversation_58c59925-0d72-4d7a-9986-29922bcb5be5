#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO问题诊断工具
快速检查和诊断YOLO数据集相关问题
"""

import os
import sys
import json
from pathlib import Path

def check_segmentation_results(base_dir):
    """检查分割结果"""
    print(f"🔍 检查分割结果: {base_dir}")
    print("-" * 40)
    
    if not os.path.exists(base_dir):
        print(f"❌ 目录不存在: {base_dir}")
        return False
    
    # 查找YOLO标注文件
    json_files = []
    image_files = []
    
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('_yolo_annotations.json'):
                json_files.append(os.path.join(root, file))
            elif file.endswith(('.png', '.jpg', '.jpeg')):
                image_files.append(os.path.join(root, file))
    
    print(f"📄 YOLO标注文件: {len(json_files)} 个")
    print(f"🖼️ 图像文件: {len(image_files)} 个")
    
    if len(json_files) == 0:
        print("❌ 没有找到YOLO标注文件")
        print("💡 解决方案:")
        print("   1. 重新进行图像分割")
        print("   2. 确保勾选'生成YOLO标注文件'")
        print("   3. 使用PNG格式的透明背景图像")
        return False
    
    # 检查标注文件内容
    valid_annotations = 0
    total_seeds = 0
    
    for json_file in json_files[:5]:  # 检查前5个文件
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'shapes' in data and len(data['shapes']) > 0:
                valid_annotations += 1
                total_seeds += len(data['shapes'])
                print(f"  ✅ {os.path.basename(json_file)}: {len(data['shapes'])} 个种子")
            else:
                print(f"  ⚠️ {os.path.basename(json_file)}: 没有标注")
                
        except Exception as e:
            print(f"  ❌ {os.path.basename(json_file)}: 读取失败 - {e}")
    
    if len(json_files) > 5:
        print(f"  ... 还有 {len(json_files) - 5} 个文件")
    
    print(f"📊 有效标注文件: {valid_annotations}/{len(json_files)}")
    print(f"🌱 总种子数: {total_seeds}")
    
    return valid_annotations > 0

def check_yolo_dataset(dataset_dir):
    """检查YOLO数据集"""
    print(f"\n🔍 检查YOLO数据集: {dataset_dir}")
    print("-" * 40)
    
    if not os.path.exists(dataset_dir):
        print(f"❌ 数据集目录不存在: {dataset_dir}")
        return False
    
    # 检查必需的文件和目录
    required_items = [
        'dataset.yaml',
        'images/train',
        'images/val', 
        'labels/train',
        'labels/val'
    ]
    
    missing_items = []
    for item in required_items:
        item_path = os.path.join(dataset_dir, item)
        if not os.path.exists(item_path):
            missing_items.append(item)
        else:
            if os.path.isdir(item_path):
                file_count = len(os.listdir(item_path))
                print(f"  ✅ {item}: {file_count} 个文件")
            else:
                print(f"  ✅ {item}: 存在")
    
    if missing_items:
        print(f"❌ 缺少项目: {', '.join(missing_items)}")
        return False
    
    # 检查dataset.yaml内容
    yaml_path = os.path.join(dataset_dir, 'dataset.yaml')
    try:
        with open(yaml_path, 'r', encoding='utf-8') as f:
            yaml_content = f.read()
        
        if 'path:' in yaml_content and 'train:' in yaml_content and 'val:' in yaml_content:
            print("  ✅ dataset.yaml 格式正确")
        else:
            print("  ⚠️ dataset.yaml 格式可能有问题")
            
    except Exception as e:
        print(f"  ❌ 读取dataset.yaml失败: {e}")
        return False
    
    return True

def suggest_solutions(has_segmentation, has_dataset):
    """建议解决方案"""
    print(f"\n💡 解决方案建议")
    print("-" * 40)
    
    if not has_segmentation:
        print("🔧 分割结果问题:")
        print("   1. 重新进行图像分割")
        print("   2. 确保勾选'生成YOLO标注文件'选项")
        print("   3. 使用PNG格式的透明背景图像")
        print("   4. 检查图像是否包含有效的种子对象")
        
    elif not has_dataset:
        print("🔧 数据集创建问题:")
        print("   1. 运行自动修复脚本:")
        print("      python fix_yolo_dataset_issues.py")
        print("   2. 或在GUI中点击'创建YOLO数据集'")
        print("   3. 选择'自动从当前输出目录创建'")
        
    else:
        print("✅ 分割结果和数据集都正常!")
        print("🎯 下一步操作:")
        print("   1. 在GUI的训练页面点击'使用最新分割数据集'")
        print("   2. 选择合适的YOLO模型进行训练")
        print("   3. 调整训练参数并开始训练")

def check_common_directories():
    """检查常见目录"""
    common_dirs = [
        "output",
        "results",
        "segmentation_results", 
        "yolo_demo_results",
        "yolo_dataset",
        "."
    ]
    
    print("🔍 检查常见目录...")
    
    found_dirs = []
    for dir_name in common_dirs:
        if os.path.exists(dir_name):
            found_dirs.append(dir_name)
            print(f"  ✅ 找到: {dir_name}")
    
    return found_dirs

def main():
    """主诊断函数"""
    print("🩺 YOLO问题诊断工具")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        base_dir = sys.argv[1]
    else:
        # 自动查找目录
        found_dirs = check_common_directories()
        
        if not found_dirs:
            print("❌ 未找到任何输出目录")
            print("💡 请指定目录: python diagnose_yolo_issues.py [目录路径]")
            return 1
        
        base_dir = found_dirs[0]  # 使用第一个找到的目录
        print(f"\n📁 使用目录: {os.path.abspath(base_dir)}")
    
    # 检查分割结果
    has_segmentation = check_segmentation_results(base_dir)
    
    # 检查YOLO数据集
    dataset_dir = os.path.join(base_dir, "yolo_dataset")
    has_dataset = check_yolo_dataset(dataset_dir)
    
    # 建议解决方案
    suggest_solutions(has_segmentation, has_dataset)
    
    # 总结
    print(f"\n📋 诊断总结")
    print("-" * 40)
    print(f"分割结果: {'✅ 正常' if has_segmentation else '❌ 有问题'}")
    print(f"YOLO数据集: {'✅ 正常' if has_dataset else '❌ 有问题'}")
    
    if has_segmentation and has_dataset:
        print(f"\n🎉 所有检查通过！可以开始YOLO训练了。")
        return 0
    else:
        print(f"\n⚠️ 发现问题，请按照上述建议解决。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
