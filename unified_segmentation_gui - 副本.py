#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unified Segmentation GUI
- SAM-based automatic segmentation
- Mask-based segmentation for pre-processed masks
- Unified interface for both approaches
"""

import os
import sys
import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading
import glob
import time

# Import segmentation modules
try:
    from sam_everything_gpu import GPUAcceleratedSAMEverything
    SAM_AVAILABLE = True
except ImportError:
    print("Warning: SAM not available")
    SAM_AVAILABLE = False

try:
    from mask_based_segmentation import MaskBasedSegmentation
    MASK_AVAILABLE = True
except ImportError:
    print("Warning: Mask-based segmentation not available")
    MASK_AVAILABLE = False

class UnifiedSegmentationGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Unified Segmentation Tool - SAM & Mask-based")
        
        # Setup window
        self.setup_window_size()
        
        # Variables
        self.current_image = None
        self.current_image_path = None
        self.current_mask_path = None
        self.current_directory = None
        self.sam_everything = None
        self.mask_segmentation = None
        self.result = None
        self.image_files = []
        self.mask_files = []
        
        # Processing variables
        self.processing = False
        
        # Create interface
        self.create_widgets()
        
        # Initialize segmentation systems
        self.initialize_systems()
    
    def setup_window_size(self):
        """Setup window size for different screen sizes"""
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        window_width = min(1500, int(screen_width * 0.9))
        window_height = min(1000, int(screen_height * 0.9))
        
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.minsize(1200, 800)
        self.root.resizable(True, True)
    
    def create_widgets(self):
        """Create main interface"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel (controls)
        self.create_left_panel(main_frame)
        
        # Right panel (image display)
        self.create_right_panel(main_frame)
    
    def create_left_panel(self, parent):
        """Create left control panel"""
        left_frame = ttk.Frame(parent)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        
        # Method selection
        self.create_method_selection(left_frame)
        
        # File management
        self.create_file_management(left_frame)
        
        # Method-specific controls
        self.create_method_controls(left_frame)
        
        # Action buttons
        self.create_action_buttons(left_frame)
        
        # Status area
        self.create_status_area(left_frame)
    
    def create_method_selection(self, parent):
        """Create method selection area"""
        method_frame = ttk.LabelFrame(parent, text="Segmentation Method")
        method_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.method_var = tk.StringVar(value="sam")
        
        # SAM option
        sam_frame = ttk.Frame(method_frame)
        sam_frame.pack(fill=tk.X, padx=5, pady=2)
        
        self.sam_radio = ttk.Radiobutton(sam_frame, text="SAM (Automatic)", 
                                        variable=self.method_var, value="sam",
                                        command=self.on_method_change)
        self.sam_radio.pack(side=tk.LEFT)
        
        if not SAM_AVAILABLE:
            self.sam_radio.config(state=tk.DISABLED)
            ttk.Label(sam_frame, text="(Not Available)", foreground="red").pack(side=tk.LEFT, padx=(5, 0))
        
        # Mask option
        mask_frame = ttk.Frame(method_frame)
        mask_frame.pack(fill=tk.X, padx=5, pady=2)
        
        self.mask_radio = ttk.Radiobutton(mask_frame, text="Mask-based", 
                                         variable=self.method_var, value="mask",
                                         command=self.on_method_change)
        self.mask_radio.pack(side=tk.LEFT)
        
        if not MASK_AVAILABLE:
            self.mask_radio.config(state=tk.DISABLED)
            ttk.Label(mask_frame, text="(Not Available)", foreground="red").pack(side=tk.LEFT, padx=(5, 0))
        
        # Method description
        self.method_desc_var = tk.StringVar()
        self.method_desc_label = ttk.Label(method_frame, textvariable=self.method_desc_var,
                                          wraplength=400, font=('Arial', 8))
        self.method_desc_label.pack(fill=tk.X, padx=5, pady=5)
        
        # Update description
        self.update_method_description()
    
    def create_file_management(self, parent):
        """Create file management area"""
        file_frame = ttk.LabelFrame(parent, text="File Management")
        file_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # File selection buttons
        button_frame = ttk.Frame(file_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="Select Images", 
                  command=self.select_images).pack(side=tk.LEFT, padx=(0, 5))
        
        self.mask_button = ttk.Button(button_frame, text="Select Masks", 
                                     command=self.select_masks)
        self.mask_button.pack(side=tk.LEFT)
        
        # Current selections
        self.image_info_var = tk.StringVar(value="No images selected")
        ttk.Label(file_frame, textvariable=self.image_info_var, 
                 wraplength=400, foreground="blue").pack(fill=tk.X, padx=5, pady=2)
        
        self.mask_info_var = tk.StringVar(value="No masks selected")
        self.mask_info_label = ttk.Label(file_frame, textvariable=self.mask_info_var,
                                        wraplength=400, foreground="green")
        self.mask_info_label.pack(fill=tk.X, padx=5, pady=2)
        
        # File list
        list_frame = ttk.Frame(file_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        ttk.Label(list_frame, text="Files:").pack(anchor=tk.W)
        
        self.file_listbox = tk.Listbox(list_frame, height=8)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.file_listbox.bind('<<ListboxSelect>>', self.on_file_select)
    
    def create_method_controls(self, parent):
        """Create method-specific control panels"""
        # Container for method controls
        self.method_controls_frame = ttk.LabelFrame(parent, text="Method Settings")
        self.method_controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # SAM controls
        self.sam_controls_frame = ttk.Frame(self.method_controls_frame)
        self.create_sam_controls(self.sam_controls_frame)
        
        # Mask controls
        self.mask_controls_frame = ttk.Frame(self.method_controls_frame)
        self.create_mask_controls(self.mask_controls_frame)
        
        # Show appropriate controls
        self.update_method_controls()
    
    def create_sam_controls(self, parent):
        """Create SAM-specific controls"""
        # Device selection
        device_frame = ttk.Frame(parent)
        device_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(device_frame, text="Device:").pack(side=tk.LEFT)
        self.device_var = tk.StringVar(value="auto")
        device_combo = ttk.Combobox(device_frame, textvariable=self.device_var,
                                   values=["auto", "gpu", "cpu"], state="readonly", width=10)
        device_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # Points per side
        points_frame = ttk.Frame(parent)
        points_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(points_frame, text="Points per side:").pack(side=tk.LEFT)
        self.points_per_side = tk.IntVar(value=32)
        ttk.Scale(points_frame, from_=16, to=64, variable=self.points_per_side,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Min area
        area_frame = ttk.Frame(parent)
        area_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(area_frame, text="Min area:").pack(side=tk.LEFT)
        self.sam_min_area = tk.IntVar(value=100)
        ttk.Scale(area_frame, from_=50, to=1000, variable=self.sam_min_area,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    def create_mask_controls(self, parent):
        """Create mask-based controls"""
        # Min seed area
        min_area_frame = ttk.Frame(parent)
        min_area_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(min_area_frame, text="Min seed area:").pack(side=tk.LEFT)
        self.mask_min_area = tk.IntVar(value=100)
        ttk.Scale(min_area_frame, from_=50, to=1000, variable=self.mask_min_area,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Max seed area
        max_area_frame = ttk.Frame(parent)
        max_area_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(max_area_frame, text="Max seed area:").pack(side=tk.LEFT)
        self.mask_max_area = tk.IntVar(value=50000)
        ttk.Scale(max_area_frame, from_=10000, to=100000, variable=self.mask_max_area,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Padding
        padding_frame = ttk.Frame(parent)
        padding_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(padding_frame, text="Padding:").pack(side=tk.LEFT)
        self.padding = tk.IntVar(value=10)
        ttk.Scale(padding_frame, from_=0, to=50, variable=self.padding,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Invert mask option
        self.invert_mask = tk.BooleanVar(value=True)
        ttk.Checkbutton(parent, text="Invert mask (black seeds on white)",
                       variable=self.invert_mask).pack(anchor=tk.W, pady=2)

    def create_action_buttons(self, parent):
        """Create action buttons"""
        button_frame = ttk.LabelFrame(parent, text="Actions")
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        # Process current
        self.process_button = ttk.Button(button_frame, text="Process Current",
                                        command=self.process_current, state=tk.DISABLED)
        self.process_button.pack(fill=tk.X, pady=2)

        # Batch process
        self.batch_button = ttk.Button(button_frame, text="Batch Process All",
                                      command=self.batch_process, state=tk.DISABLED)
        self.batch_button.pack(fill=tk.X, pady=2)

        # Output directory
        output_frame = ttk.Frame(button_frame)
        output_frame.pack(fill=tk.X, pady=5)

        ttk.Label(output_frame, text="Output:").pack(anchor=tk.W)
        self.output_dir_var = tk.StringVar(value="output")
        ttk.Entry(output_frame, textvariable=self.output_dir_var).pack(fill=tk.X, pady=2)
        ttk.Button(output_frame, text="Browse", command=self.select_output_dir).pack(fill=tk.X, pady=2)

    def create_status_area(self, parent):
        """Create status information area"""
        status_frame = ttk.LabelFrame(parent, text="Status")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        # Status text
        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(status_frame, textvariable=self.status_var, font=('Arial', 9, 'bold')).pack(anchor=tk.W, padx=5, pady=2)

        # Progress bar
        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=2)

        # Results info
        self.results_var = tk.StringVar(value="")
        ttk.Label(status_frame, textvariable=self.results_var, font=('Arial', 8)).pack(anchor=tk.W, padx=5, pady=2)

    def create_right_panel(self, parent):
        """Create right panel for image display"""
        right_frame = ttk.Frame(parent)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Image display area
        image_frame = ttk.LabelFrame(right_frame, text="Image Preview")
        image_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Canvas for image display
        self.image_canvas = tk.Canvas(image_frame, bg='white')
        self.image_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Image info
        self.image_info_display_var = tk.StringVar(value="No image loaded")
        ttk.Label(image_frame, textvariable=self.image_info_display_var).pack(pady=2)

    def initialize_systems(self):
        """Initialize segmentation systems"""
        if SAM_AVAILABLE:
            try:
                sam_config = {
                    'checkpoint_path': 'sam_vit_h_4b8939.pth',
                    'device': 'auto',
                    'points_per_side': 32,
                    'min_object_area': 100,
                    'use_mask_extraction': True,
                    'generate_yolo_labels': True,
                }
                self.sam_everything = GPUAcceleratedSAMEverything(sam_config)
                self.log_message("SAM system initialized")
            except Exception as e:
                self.log_message(f"SAM initialization failed: {e}")
                self.sam_everything = None

        if MASK_AVAILABLE:
            try:
                mask_config = {
                    'min_seed_area': 100,
                    'max_seed_area': 50000,
                    'padding': 10,
                    'invert_mask': True,
                }
                self.mask_segmentation = MaskBasedSegmentation(mask_config)
                self.log_message("Mask-based system initialized")
            except Exception as e:
                self.log_message(f"Mask system initialization failed: {e}")
                self.mask_segmentation = None

    def on_method_change(self):
        """Handle method selection change"""
        self.update_method_description()
        self.update_method_controls()
        self.update_file_display()
        self.update_button_states()

    def update_method_description(self):
        """Update method description text"""
        method = self.method_var.get()
        if method == "sam":
            desc = "SAM (Segment Anything Model) automatically detects and segments all objects in an image using AI."
        else:
            desc = "Mask-based segmentation processes pre-made mask images where seeds appear as black regions on white background."
        self.method_desc_var.set(desc)

    def update_method_controls(self):
        """Show/hide method-specific controls"""
        # Hide all controls first
        self.sam_controls_frame.pack_forget()
        self.mask_controls_frame.pack_forget()

        # Show appropriate controls
        method = self.method_var.get()
        if method == "sam":
            self.sam_controls_frame.pack(fill=tk.X, padx=5, pady=5)
            self.mask_button.config(state=tk.DISABLED)
            self.mask_info_label.pack_forget()
        else:
            self.mask_controls_frame.pack(fill=tk.X, padx=5, pady=5)
            self.mask_button.config(state=tk.NORMAL)
            self.mask_info_label.pack(fill=tk.X, padx=5, pady=2)

    def update_file_display(self):
        """Update file list display based on current method"""
        self.file_listbox.delete(0, tk.END)

        method = self.method_var.get()
        if method == "sam":
            # Show image files
            for img_file in self.image_files:
                self.file_listbox.insert(tk.END, os.path.basename(img_file))
        else:
            # Show matched pairs
            matched_pairs = self.get_matched_pairs()
            for img_file, mask_file in matched_pairs:
                display_name = f"{os.path.basename(img_file)} + {os.path.basename(mask_file) if mask_file else 'NO MASK'}"
                self.file_listbox.insert(tk.END, display_name)

    def update_button_states(self):
        """Update button states based on current selections"""
        method = self.method_var.get()
        has_images = len(self.image_files) > 0

        if method == "sam":
            can_process = has_images and self.sam_everything is not None
        else:
            matched_pairs = self.get_matched_pairs()
            can_process = len(matched_pairs) > 0 and self.mask_segmentation is not None

        state = tk.NORMAL if can_process and not self.processing else tk.DISABLED
        self.process_button.config(state=state)
        self.batch_button.config(state=state)

    def get_matched_pairs(self):
        """Get matched image-mask pairs for mask-based processing"""
        if not self.image_files or not self.mask_files:
            return []

        # Create dictionaries based on filename stems
        image_dict = {os.path.splitext(os.path.basename(f))[0]: f for f in self.image_files}
        mask_dict = {os.path.splitext(os.path.basename(f))[0]: f for f in self.mask_files}

        # Find matches
        matched_pairs = []
        for stem in image_dict.keys():
            mask_file = mask_dict.get(stem)
            matched_pairs.append((image_dict[stem], mask_file))

        return matched_pairs

    def select_images(self):
        """Select image files"""
        filetypes = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("All files", "*.*")
        ]

        files = filedialog.askopenfilenames(title="Select Images", filetypes=filetypes)
        if files:
            self.image_files = list(files)
            self.image_info_var.set(f"Selected {len(self.image_files)} images")
            self.update_file_display()
            self.update_button_states()

    def select_masks(self):
        """Select mask files"""
        filetypes = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("All files", "*.*")
        ]

        files = filedialog.askopenfilenames(title="Select Mask Images", filetypes=filetypes)
        if files:
            self.mask_files = list(files)
            self.mask_info_var.set(f"Selected {len(self.mask_files)} masks")
            self.update_file_display()
            self.update_button_states()

    def select_output_dir(self):
        """Select output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir_var.set(directory)

    def on_file_select(self, event):
        """Handle file selection in listbox"""
        selection = self.file_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        method = self.method_var.get()

        if method == "sam":
            if index < len(self.image_files):
                self.load_and_display_image(self.image_files[index])
        else:
            matched_pairs = self.get_matched_pairs()
            if index < len(matched_pairs):
                img_file, mask_file = matched_pairs[index]
                self.load_and_display_image(img_file)
                if mask_file:
                    self.current_mask_path = mask_file

    def load_and_display_image(self, image_path):
        """Load and display an image"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                self.log_message(f"Failed to load image: {image_path}")
                return

            self.current_image = image
            self.current_image_path = image_path

            # Convert for display
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # Resize for display
            canvas_width = self.image_canvas.winfo_width()
            canvas_height = self.image_canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                h, w = image_rgb.shape[:2]
                scale = min(canvas_width / w, canvas_height / h, 1.0)

                if scale < 1.0:
                    new_w = int(w * scale)
                    new_h = int(h * scale)
                    image_rgb = cv2.resize(image_rgb, (new_w, new_h))

                # Convert to PhotoImage
                pil_image = Image.fromarray(image_rgb)
                self.photo = ImageTk.PhotoImage(pil_image)

                # Display on canvas
                self.image_canvas.delete("all")
                self.image_canvas.create_image(canvas_width//2, canvas_height//2,
                                             image=self.photo, anchor=tk.CENTER)

            # Update info
            h, w = image.shape[:2]
            filename = os.path.basename(image_path)
            self.image_info_display_var.set(f"{filename} - {w}x{h}")

        except Exception as e:
            self.log_message(f"Error loading image: {e}")

    def process_current(self):
        """Process current selected image"""
        if self.processing:
            return

        method = self.method_var.get()

        if method == "sam":
            if not self.current_image_path or self.sam_everything is None:
                messagebox.showerror("Error", "No image selected or SAM not available")
                return

            # Process with SAM in thread
            threading.Thread(target=self._process_sam_single, daemon=True).start()

        else:
            if not self.current_image_path or not self.current_mask_path or self.mask_segmentation is None:
                messagebox.showerror("Error", "Image and mask must be selected, or mask system not available")
                return

            # Process with mask-based in thread
            threading.Thread(target=self._process_mask_single, daemon=True).start()

    def batch_process(self):
        """Batch process all files"""
        if self.processing:
            return

        method = self.method_var.get()

        if method == "sam":
            if not self.image_files or self.sam_everything is None:
                messagebox.showerror("Error", "No images selected or SAM not available")
                return

            threading.Thread(target=self._batch_process_sam, daemon=True).start()

        else:
            matched_pairs = self.get_matched_pairs()
            if not matched_pairs or self.mask_segmentation is None:
                messagebox.showerror("Error", "No matched pairs or mask system not available")
                return

            threading.Thread(target=self._batch_process_mask, daemon=True).start()

    def _process_sam_single(self):
        """Process single image with SAM (runs in thread)"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())
        self.root.after(0, lambda: self.status_var.set("Processing with SAM..."))

        try:
            # Update SAM config
            config = {
                'checkpoint_path': 'sam_vit_h_4b8939.pth',
                'device': self.device_var.get(),
                'points_per_side': self.points_per_side.get(),
                'min_object_area': self.sam_min_area.get(),
                'use_mask_extraction': True,
                'generate_yolo_labels': True,
            }

            # Reinitialize if needed
            if self.sam_everything is None:
                self.sam_everything = GPUAcceleratedSAMEverything(config)

            # Create output directory
            output_dir = self.output_dir_var.get()
            base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
            image_output_dir = os.path.join(output_dir, base_name)

            # Process
            result = self.sam_everything.process_image(self.current_image, self.current_image_path, image_output_dir)

            # Update UI
            if result['success']:
                self.root.after(0, lambda: self.status_var.set("SAM processing completed"))
                self.root.after(0, lambda: self.results_var.set(
                    f"Objects: {result['objects_count']}, Time: {result['processing_time']:.2f}s"
                ))
                self.root.after(0, lambda: messagebox.showinfo("Success",
                    f"Processing completed!\nObjects detected: {result['objects_count']}\nOutput: {image_output_dir}"))
            else:
                self.root.after(0, lambda: self.status_var.set("SAM processing failed"))
                self.root.after(0, lambda: messagebox.showerror("Error", f"Processing failed: {result.get('error', 'Unknown error')}"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set("SAM processing error"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Processing error: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def _process_mask_single(self):
        """Process single image with mask-based segmentation (runs in thread)"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())
        self.root.after(0, lambda: self.status_var.set("Processing with mask-based segmentation..."))

        try:
            # Update mask config
            config = {
                'min_seed_area': self.mask_min_area.get(),
                'max_seed_area': self.mask_max_area.get(),
                'padding': self.padding.get(),
                'invert_mask': self.invert_mask.get(),
                'output_format': 'png',
            }

            # Reinitialize if needed
            if self.mask_segmentation is None:
                self.mask_segmentation = MaskBasedSegmentation(config)

            # Create output directory
            output_dir = self.output_dir_var.get()
            base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
            image_output_dir = os.path.join(output_dir, base_name)

            # Process
            result = self.mask_segmentation.process_mask_and_original(
                self.current_mask_path, self.current_image_path, image_output_dir
            )

            # Update UI
            if result['success']:
                self.root.after(0, lambda: self.status_var.set("Mask-based processing completed"))
                self.root.after(0, lambda: self.results_var.set(
                    f"Seeds: {result['seeds_count']}, Scale bars: {result['scale_bars_count']}, Time: {result['processing_time']:.2f}s"
                ))
                self.root.after(0, lambda: messagebox.showinfo("Success",
                    f"Processing completed!\nSeeds extracted: {result['seeds_count']}\nScale bars: {result['scale_bars_count']}\nOutput: {image_output_dir}"))
            else:
                self.root.after(0, lambda: self.status_var.set("Mask-based processing failed"))
                self.root.after(0, lambda: messagebox.showerror("Error", f"Processing failed: {result.get('error', 'Unknown error')}"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set("Mask-based processing error"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Processing error: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def _batch_process_sam(self):
        """Batch process images with SAM (runs in thread)"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())

        try:
            output_dir = self.output_dir_var.get()
            total_files = len(self.image_files)
            successful = 0

            for i, image_path in enumerate(self.image_files):
                self.root.after(0, lambda i=i, total=total_files: self.status_var.set(f"Processing {i+1}/{total}..."))

                # Load image
                image = cv2.imread(image_path)
                if image is None:
                    continue

                # Create output directory
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                image_output_dir = os.path.join(output_dir, base_name)

                # Process
                result = self.sam_everything.process_image(image, image_path, image_output_dir)
                if result['success']:
                    successful += 1

            # Show results
            self.root.after(0, lambda: self.status_var.set("Batch processing completed"))
            self.root.after(0, lambda: self.results_var.set(f"Processed: {successful}/{total_files}"))
            self.root.after(0, lambda: messagebox.showinfo("Batch Complete",
                f"Batch processing completed!\nSuccessful: {successful}/{total_files}"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Batch processing error: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def _batch_process_mask(self):
        """Batch process with mask-based segmentation (runs in thread)"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())

        try:
            matched_pairs = self.get_matched_pairs()
            output_dir = self.output_dir_var.get()
            total_pairs = len(matched_pairs)
            successful = 0
            total_seeds = 0

            for i, (image_path, mask_path) in enumerate(matched_pairs):
                if mask_path is None:
                    continue

                self.root.after(0, lambda i=i, total=total_pairs: self.status_var.set(f"Processing pair {i+1}/{total}..."))

                # Process
                result = self.mask_segmentation.process_mask_and_original(
                    mask_path, image_path, output_dir
                )

                if result['success']:
                    successful += 1
                    total_seeds += result['seeds_count']

            # Show results
            self.root.after(0, lambda: self.status_var.set("Batch processing completed"))
            self.root.after(0, lambda: self.results_var.set(f"Processed: {successful}/{total_pairs}, Seeds: {total_seeds}"))
            self.root.after(0, lambda: messagebox.showinfo("Batch Complete",
                f"Batch processing completed!\nSuccessful: {successful}/{total_pairs}\nTotal seeds: {total_seeds}"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Batch processing error: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def log_message(self, message):
        """Log a message to status"""
        print(f"[GUI] {message}")
        self.status_var.set(message)

def main():
    """Main function to run the GUI"""
    root = tk.Tk()
    app = UnifiedSegmentationGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
