# GPU-accelerated SAM Everything (SAME)

## 概述

GPU-accelerated SAM Everything 是一个专注于对象提取和 YOLO 标签生成的高性能工具，提供了 GPU 加速、设备选择和自动化标注功能。

### 🚀 新版本特性

✅ **GPU/CPU 设备选择** - 支持命令行和 GUI 界面选择处理设备
✅ **CUDA 11.2 兼容** - 完全兼容 CUDA 11.2，充分利用 GPU 性能
✅ **YOLO 标签生成** - 自动生成 YOLO 格式的边界框标注文件
✅ **官方 SAM 参数** - 使用 segment-anything.com 官方 demo 的默认参数
✅ **性能监控** - 实时显示处理时间和 GPU 使用状态
✅ **命令行界面** - 支持批量处理和自动化脚本

### 📋 原有特性

✅ **简化的处理流程** - 直接使用 SAM Everything 进行自动分割，跳过复杂的分类步骤
✅ **自动对象提取** - 将所有检测到的对象自动裁剪并保存为独立图像
✅ **增强的文件管理** - 图像文件列表，点击加载，批量处理
✅ **更大的图像显示** - 提高可视性，更好的用户体验
✅ **中文路径支持** - 完美支持包含中文字符的文件路径
✅ **批量处理能力** - 一次处理整个文件夹的图像

## 文件结构

```
SAME/
├── sam_everything_gpu.py                 # 🆕 GPU加速核心处理类
├── sam_gpu_cli.py                        # 🆕 命令行界面
├── sam_everything_gpu_gui.py             # 🆕 GPU加速GUI界面
├── requirements.txt                      # 🆕 依赖包列表 (CUDA 11.2)
├── sam_everything_simplified.py          # 原始核心处理类
├── sam_everything_gui_simplified.py      # 原始GUI界面
├── quick_fix_chinese_path.py             # 中文路径修复工具
├── README.md                             # 本文件
└── sam_vit_h_4b8939.pth                 # SAM模型文件（需要下载）
```

## 🆕 新功能详解

### GPU/CPU 设备选择

- **自动选择**: 自动检测 GPU 可用性，优先使用 GPU
- **强制 GPU**: 强制使用 GPU，如果不可用则报错
- **强制 CPU**: 强制使用 CPU 处理

### YOLO 标签生成

- **自动生成**: 为每个检测对象生成 YOLO 格式边界框
- **标准格式**: `class_id center_x center_y width height` (归一化坐标)
- **文件组织**: 标签文件保存在 `yolo_label/` 目录中
- **批量处理**: 支持批量生成多个图像的标签文件

### 官方 SAM 参数

使用 segment-anything.com 官方 demo 的默认参数：

- `points_per_side`: 32
- `pred_iou_thresh`: 0.88
- `stability_score_thresh`: 0.95
- `crop_n_layers`: 1
- `min_mask_region_area`: 100

## 快速开始

### 1. 环境准备

#### 🆕 GPU 版本 (推荐)

```bash
# 安装CUDA 11.2兼容的依赖
pip install -r requirements.txt

# 或者手动安装
pip install torch==1.12.1+cu112 torchvision==0.13.1+cu112 torchaudio==0.12.1+cu112 --extra-index-url https://download.pytorch.org/whl/cu112
pip install segment-anything opencv-python pillow numpy

# 检查GPU可用性
python sam_gpu_cli.py --check-gpu
```

#### 原版本 (CPU)

```bash
# 安装基础依赖
pip install opencv-python numpy pillow

# 安装SAM依赖
pip install segment-anything torch torchvision

# 下载SAM模型文件
# 将 sam_vit_h_4b8939.pth 放在SAME目录中
```

### 2. 下载 SAM 模型

```bash
# 下载SAM ViT-H模型 (约2.6GB)
wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

# 或使用curl
curl -L -o sam_vit_h_4b8939.pth https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
```

### 3. 使用方法

#### 🆕 命令行界面 (推荐)

```bash
# 基本用法 - 处理单个图像
python sam_gpu_cli.py --device gpu --input image.jpg --output ./results

# 批量处理文件夹
python sam_gpu_cli.py --device gpu --input ./images --output ./results --batch

# 使用CPU处理
python sam_gpu_cli.py --device cpu --input image.jpg --output ./results

# 自定义参数
python sam_gpu_cli.py --device gpu --input image.jpg --output ./results \
    --points-per-side 64 --min-area 200 --class-id 1

# 禁用YOLO标签生成
python sam_gpu_cli.py --device gpu --input image.jpg --output ./results --no-yolo-labels

# 查看所有选项
python sam_gpu_cli.py --help
```

#### 🆕 GPU 加速 GUI 界面

```bash
cd SAME
python sam_everything_gpu_gui.py
```

#### 原版 GUI 界面

```bash
cd SAME
python sam_everything_gui_simplified.py
```

### 4. 基本使用流程

1. **选择文件/文件夹**

   - 点击"选择文件夹"浏览整个目录
   - 或点击"选择单个文件"处理单张图像

2. **浏览图像文件**

   - 左侧列表显示所有图像文件
   - 点击文件名即可加载图像

3. **调整参数**（可选）

   - 每边点数：控制分割密度
   - 最小对象面积：过滤小对象
   - 可视化选项：掩膜显示和透明度

4. **处理图像**

   - "处理当前图像"：处理选中的图像
   - "批量处理所有图像"：处理文件夹中所有图像

5. **查看结果**
   - 对象文件：object_001.jpg, object_002.jpg, ...
   - 结果图像：原图名\_result.jpg

## 中文路径支持

如果遇到中文路径问题，运行修复工具：

```bash
python quick_fix_chinese_path.py
```

该工具会：

- 自动检测中文路径问题
- 将图像文件复制到安全的英文路径
- 生成使用说明

## 🆕 输出结果

### 新版本输出结构

```
output/
├── image1/                    # 以原图名命名的子目录
│   ├── object_001.jpg        # 第1个检测对象
│   ├── object_002.jpg        # 第2个检测对象
│   ├── ...
│   ├── image1_result.jpg     # 可视化结果图像
│   └── yolo_label/           # 🆕 YOLO标签目录
│       └── image1.txt        # 🆕 YOLO格式标签文件
├── image2/
│   ├── object_001.jpg
│   ├── ...
│   ├── image2_result.jpg
│   └── yolo_label/
│       └── image2.txt
└── ...
```

### YOLO 标签文件格式

每个`.txt`文件包含检测到的所有对象，每行一个对象：

```
0 0.5 0.3 0.2 0.4    # class_id center_x center_y width height
0 0.7 0.6 0.15 0.25  # 所有坐标都是归一化的 [0,1]
0 0.2 0.8 0.3 0.1
```

### 性能对比

| 处理模式    | 设备     | 单图处理时间\* | 批量处理优势 |
| ----------- | -------- | -------------- | ------------ |
| 🆕 GPU 加速 | RTX 3080 | ~2-5 秒        | 🚀 3-5x 加速 |
| 🆕 GPU 加速 | RTX 4090 | ~1-3 秒        | 🚀 5-8x 加速 |
| 原版本      | CPU      | ~15-30 秒      | 基准性能     |

\*处理时间取决于图像大小和复杂度

## 配置选项

### 🆕 GPU 版本默认配置 (官方 SAM demo 参数)

- 每边点数：32
- 预测 IoU 阈值：0.88
- 稳定性分数阈值：0.95
- 最小对象面积：100 像素
- 显示掩膜：开启
- 掩膜透明度：0.35
- YOLO 标签生成：开启
- 默认类别 ID：0

### 参数调优建议

- **高质量模式**：每边点数 64，最小面积 200，稳定性阈值 0.98
- **快速模式**：每边点数 16，最小面积 50，稳定性阈值 0.90
- **大对象模式**：最小面积 1000，预测 IoU 阈值 0.95
- **小对象模式**：最小面积 25，每边点数 64

### 🆕 命令行参数完整列表

```bash
python sam_gpu_cli.py --help

# 主要参数：
--device {cpu,gpu,cuda,auto}     # 设备选择
--input PATH                     # 输入文件或目录
--output PATH                    # 输出目录
--batch                          # 批量处理模式
--points-per-side INT            # 每边点数 (16-64)
--pred-iou-thresh FLOAT          # 预测IoU阈值 (0.5-1.0)
--stability-score-thresh FLOAT   # 稳定性分数阈值 (0.5-1.0)
--min-area INT                   # 最小对象面积
--class-id INT                   # YOLO默认类别ID
--no-yolo-labels                 # 禁用YOLO标签生成
--no-masks                       # 禁用掩膜可视化
--verbose                        # 详细日志输出
```

## 系统要求

### 🆕 GPU 版本 (推荐)

- Python 3.7+
- CUDA 11.2 或更高版本
- 16GB+ 内存 (推荐 32GB)
- NVIDIA GPU (8GB+ 显存推荐)
- 支持的 GPU: RTX 20/30/40 系列, Tesla V100, A100 等

### 原版本 (CPU)

- Python 3.7+
- 8GB+ 内存（推荐 16GB）
- CUDA 支持的 GPU（可选，但强烈推荐）

## 故障排除

### 常见问题

1. **中文路径问题**

   ```bash
   python quick_fix_chinese_path.py
   ```

2. **内存不足**

   - 降低每边点数
   - 处理更小的图像

3. **处理速度慢**

   - 使用 GPU
   - 降低分割密度

4. **对象过多**
   - 提高最小对象面积

### 🆕 GPU 相关问题

1. **CUDA 不可用**

   ```bash
   # 检查CUDA安装
   nvidia-smi
   python sam_gpu_cli.py --check-gpu

   # 重新安装PyTorch with CUDA
   pip uninstall torch torchvision torchaudio
   pip install torch==1.12.1+cu112 torchvision==0.13.1+cu112 --extra-index-url https://download.pytorch.org/whl/cu112
   ```

2. **显存不足 (CUDA out of memory)**

   ```bash
   # 降低处理参数
   python sam_gpu_cli.py --device gpu --points-per-side 16 --min-area 200

   # 或强制使用CPU
   python sam_gpu_cli.py --device cpu
   ```

3. **YOLO 标签问题**
   - 检查 yolo_label 文件夹是否生成
   - 验证标签文件格式
   - 使用 --class-id 设置正确的类别 ID

### 🆕 环境检查

```bash
# 基础环境检查
python -c "import cv2, numpy, PIL; print('基础库正常')"
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
python -c "from segment_anything import sam_model_registry; print('SAM正常')"

# GPU详细检查
python sam_gpu_cli.py --check-gpu

# 完整功能测试
python sam_gpu_cli.py --device auto --input test_image.jpg --output test_output --verbose
```

## 使用场景

### 🆕 新增应用场景

- **YOLO 数据集制作**：自动生成大量标注数据，加速模型训练
- **实时对象检测**：GPU 加速的快速分割，适合实时应用
- **大规模图像处理**：批量处理数千张图像，生成标准化数据集
- **自动化标注流水线**：集成到 CI/CD 中，自动处理新图像

### 传统应用场景

- **科学研究**：种子、细胞、颗粒等对象的自动提取
- **数据集制作**：从大图像中提取小对象
- **质量检测**：产品缺陷检测，零件分拣
- **图像分析**：医学图像、遥感图像、显微镜图像分析

## 🆕 技术特点

### GPU 加速版本

- **🚀 GPU 加速**：充分利用 NVIDIA GPU，处理速度提升 3-8 倍
- **⚡ 设备灵活性**：支持 GPU/CPU 自由切换，适应不同硬件环境
- **🎯 YOLO 集成**：自动生成 YOLO 格式标签，无缝对接目标检测训练
- **📊 性能监控**：实时显示处理时间和资源使用情况
- **🔧 参数优化**：使用官方 SAM demo 参数，确保最佳分割效果
- **📝 命令行支持**：支持脚本化和自动化处理

### 通用特点

- **零配置使用**：默认参数即可获得良好效果
- **文件夹浏览**：像文件管理器一样浏览图像
- **一键批处理**：处理整个文件夹的图像
- **实时预览**：更大的图像显示区域
- **自动命名**：规范的文件命名和目录结构
- **中文路径兼容**：完美支持中文文件路径

---

## 🎉 版本特点总结

### 🆕 GPU 加速版本

- ✅ **GPU/CPU 设备选择** - 灵活的硬件适配
- ✅ **YOLO 标签自动生成** - 标准化目标检测数据集
- ✅ **命令行界面** - 支持自动化和批处理脚本
- ✅ **官方 SAM 参数** - 最优的分割质量
- ✅ **性能监控** - 实时处理时间显示
- ✅ **CUDA 11.2 兼容** - 稳定的 GPU 加速支持

### 📋 原版本特点

- ✅ 更简单的工作流程
- ✅ 更好的文件管理
- ✅ 更大的图像显示
- ✅ 更强的批量处理能力
- ✅ 更直观的用户界面
- ✅ 完整的中文路径支持

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看故障排除部分
2. 运行环境检查命令
3. 提交 Issue 并附上详细的错误信息和系统配置

**推荐使用 GPU 加速版本以获得最佳性能和完整功能！**
