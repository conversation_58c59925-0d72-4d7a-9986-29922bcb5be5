# YOLO训练功能使用指南

## 🎯 概述

透明背景种子分割系统现在支持生成YOLO训练格式的数据，包括：
- **智能文件命名**: 文件名+种子数量的命名格式
- **标记区域裁剪**: 保存带边界框的单独图片
- **YOLO标注文件**: 生成训练用的JSON格式标注
- **完整数据集**: 自动创建YOLO训练数据集

## ✨ 新功能特性

### 📝 智能文件命名
种子文件现在使用更有意义的命名格式：
```
原文件名_种子总数_种子ID.png
```

**示例**:
- `小麦样本_5seeds_001.png` - 第1个种子（共5个）
- `小麦样本_5seeds_002.png` - 第2个种子（共5个）
- `小麦样本_5seeds_003.png` - 第3个种子（共5个）

### 🖼️ 标记区域裁剪
自动生成带边界框标记的裁剪图片：
- 包含种子及其周围区域
- 绘制绿色边界框
- 添加种子编号标签
- 保存为独立PNG文件

### 📄 YOLO标注文件
生成标准的YOLO训练标注：
- JSON格式，兼容主流标注工具
- 包含边界框坐标
- 支持多边形标注
- 自动类别标记

### 📊 完整YOLO数据集
一键创建完整的训练数据集：
- 自动分割训练集和验证集
- 生成YOLO配置文件
- 创建标准目录结构
- 包含数据集验证

## 🚀 使用方法

### 命令行使用

#### 基本处理（包含新功能）
```bash
# 处理单张图像
python transparent_seed_cli.py --input seeds.png --output results

# 批量处理
python transparent_seed_cli.py --input ./images --output results --batch
```

#### 创建YOLO数据集
```bash
# 处理图像并直接创建YOLO数据集
python transparent_seed_cli.py --input ./images --output results --batch --create-yolo-dataset ./yolo_dataset

# 从已有结果创建YOLO数据集
python yolo_format_converter.py --source results --output yolo_dataset --train-ratio 0.8
```

#### 验证数据集
```bash
python yolo_format_converter.py --source results --output yolo_dataset --validate
```

### 图形界面使用

1. **启动GUI**:
   ```bash
   python enhanced_segmentation_gui.py
   ```

2. **选择透明背景分割方法**

3. **启用YOLO功能**:
   - ✅ 生成YOLO标注文件
   - ✅ 保存标记裁剪图片

4. **处理图像**

### Python API使用

```python
from transparent_seed_segmentation import TransparentSeedSegmentation
from yolo_format_converter import YOLOFormatConverter

# 配置分割系统
config = {
    'alpha_threshold': 50,
    'min_seed_area': 100,
    'max_seed_area': 50000,
    'padding': 15,
}

# 处理图像
segmentation = TransparentSeedSegmentation(config)
result = segmentation.process_transparent_image("seeds.png", "output")

if result['success']:
    print(f"提取种子: {result['seeds_count']}")
    print(f"YOLO JSON: {result['yolo_json_path']}")
    print(f"标记裁剪: {result['marked_crops_dir']}")

# 创建YOLO数据集
converter = YOLOFormatConverter()
dataset_info = converter.create_yolo_dataset("output", "yolo_dataset")
```

## 📁 输出文件结构

### 基本输出
```
output/
├── 图像名/
│   ├── 图像名_5seeds_001.png          # 种子1（新命名格式）
│   ├── 图像名_5seeds_002.png          # 种子2
│   ├── 图像名_5seeds_003.png          # 种子3
│   ├── 图像名_5seeds_004.png          # 种子4
│   ├── 图像名_5seeds_005.png          # 种子5
│   ├── 图像名_yolo_annotations.json   # YOLO标注文件
│   ├── 图像名_transparent_segmentation.jpg  # 可视化结果
│   └── 图像名_marked_crops/           # 标记裁剪目录
│       ├── 图像名_5seeds_marked_001.png    # 标记裁剪1
│       ├── 图像名_5seeds_marked_002.png    # 标记裁剪2
│       └── ...
```

### YOLO数据集结构
```
yolo_dataset/
├── images/
│   ├── train/                    # 训练图像
│   │   ├── image1.png
│   │   └── image2.png
│   └── val/                      # 验证图像
│       ├── image3.png
│       └── image4.png
├── labels/
│   ├── train/                    # 训练标签
│   │   ├── image1.txt
│   │   └── image2.txt
│   └── val/                      # 验证标签
│       ├── image3.txt
│       └── image4.txt
├── dataset.yaml                  # 数据集配置
└── classes.txt                   # 类别文件
```

## 📋 YOLO标注格式

### JSON格式（中间格式）
```json
{
  "version": "1.0",
  "shapes": [
    {
      "label": "seed",
      "points": [[x1, y1], [x2, y2], [x3, y3], [x4, y4]],
      "shape_type": "polygon",
      "description": "Seed 1, Area: 1250 pixels"
    }
  ],
  "imageWidth": 800,
  "imageHeight": 600,
  "imagePath": "seeds.png"
}
```

### TXT格式（YOLO标准）
```
# class_id center_x center_y width height (归一化坐标)
0 0.425000 0.350000 0.125000 0.200000
0 0.675000 0.450000 0.150000 0.180000
```

## ⚙️ 配置选项

### 文件命名配置
- 自动根据检测到的种子数量命名
- 格式：`{原文件名}_{种子总数}seeds_{种子ID:03d}.png`
- 种子ID从001开始递增

### 标记裁剪配置
- **边距**: 可配置的裁剪边距（默认15像素）
- **边界框颜色**: 绿色（0, 255, 0）
- **标签字体**: OpenCV默认字体
- **输出格式**: PNG（保持透明度）

### YOLO数据集配置
- **训练/验证比例**: 默认8:2，可调整
- **类别名称**: 默认"seed"，可自定义
- **数据集格式**: 标准YOLO v5/v8格式

## 🧪 演示和测试

### 运行完整演示
```bash
python demo_yolo_features.py
```

这会：
1. 创建演示图像（小麦、玉米、大豆种子）
2. 处理图像并生成所有新功能输出
3. 创建完整的YOLO数据集
4. 验证数据集完整性
5. 显示文件结构和使用说明

### 测试新功能
```bash
# 创建测试图像
python debug_transparent_segmentation.py --create-test

# 处理测试图像
python transparent_seed_cli.py --input debug_test_transparent.png --output test_results --create-yolo-dataset test_yolo
```

## 🎯 YOLO训练建议

### 数据准备
1. **图像质量**: 确保种子边界清晰
2. **数量充足**: 建议每类至少100张图像
3. **多样性**: 包含不同光照、角度、背景
4. **标注准确**: 验证边界框位置正确

### 训练参数
```yaml
# 推荐的YOLO训练配置
epochs: 100
batch_size: 16
img_size: 640
lr0: 0.01
weight_decay: 0.0005
```

### 模型选择
- **YOLOv8n**: 快速检测，适合实时应用
- **YOLOv8s**: 平衡速度和精度
- **YOLOv8m**: 高精度检测

## 🔧 故障排除

### 常见问题

#### YOLO文件未生成
**原因**: 种子检测失败
**解决**: 调整透明背景分割参数

#### 数据集验证失败
**原因**: 图像和标签文件不匹配
**解决**: 检查文件命名和路径

#### 训练精度低
**原因**: 数据质量或数量不足
**解决**: 增加数据多样性，检查标注质量

### 调试工具
```bash
# 检查分割结果
python debug_transparent_segmentation.py image.png

# 验证YOLO数据集
python yolo_format_converter.py --source results --output dataset --validate

# 查看详细日志
python transparent_seed_cli.py --input image.png --output results --verbose
```

## 📚 相关文档

- **[透明背景分割指南](TRANSPARENT_SEGMENTATION_GUIDE.md)**: 基础分割功能
- **[增强GUI指南](ENHANCED_GUI_GUIDE.md)**: 图形界面使用
- **[故障排除指南](TROUBLESHOOT_NO_SEEDS.md)**: 问题解决方案

## 🔗 外部资源

- **[YOLOv8官方文档](https://docs.ultralytics.com/)**
- **[YOLO数据格式说明](https://docs.ultralytics.com/datasets/)**
- **[种子检测最佳实践](https://github.com/ultralytics/ultralytics)**

---

**版本**: v2.0  
**更新日期**: 2024年  
**新功能**: YOLO训练支持，智能命名，标记裁剪
