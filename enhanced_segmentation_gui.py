#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Unified Segmentation GUI (Chinese Interface)
- Chinese localization for all interface elements
- Enhanced file management with automatic folder scanning
- Automatic mask-image binding with intelligent pairing
- Visual pairing indicators and manual override options
"""

import os
import sys
import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading
import glob
import time
from pathlib import Path
import re

# Import segmentation modules
try:
    from sam_everything_gpu import GPUAcceleratedSAMEverything
    SAM_AVAILABLE = True
except ImportError:
    print("警告: SAM模块不可用")
    SAM_AVAILABLE = False

try:
    from mask_based_segmentation import MaskBasedSegmentation
    MASK_AVAILABLE = True
except ImportError:
    print("警告: 掩膜分割模块不可用")
    MASK_AVAILABLE = False

try:
    from transparent_seed_segmentation import TransparentSeedSegmentation
    TRANSPARENT_AVAILABLE = True
except ImportError:
    print("警告: 透明背景分割模块不可用")
    TRANSPARENT_AVAILABLE = False

# 导入YOLO相关组件
try:
    from yolo_manager import YOLOManager
    from yolo_gui_components import YOLOModelSelector, YOLOTrainingPanel, YOLOPredictionPanel
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("警告: YOLO组件不可用，相关功能将被禁用")

class EnhancedSegmentationGUI:
    """增强的统一分割GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("统一分割工具 - SAM与掩膜分割")
        
        # 设置窗口
        self.setup_window_size()
        
        # 变量
        self.current_image = None
        self.current_image_path = None
        self.current_mask_path = None
        self.current_folder = None
        self.sam_everything = None
        self.mask_segmentation = None
        self.transparent_segmentation = None
        self.yolo_manager = None
        self.result = None
        
        # 文件管理变量
        self.image_files = []
        self.mask_files = []
        self.paired_files = {}  # {original_path: mask_path}
        self.unpaired_files = []
        
        # 处理状态
        self.processing = False
        
        # 支持的图像格式
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        
        # 创建界面
        self.create_widgets()
        
        # 初始化分割系统
        self.initialize_systems()
    
    def setup_window_size(self):
        """设置适配不同屏幕尺寸的窗口大小"""
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        window_width = min(1600, int(screen_width * 0.9))
        window_height = min(1000, int(screen_height * 0.9))
        
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.minsize(1200, 800)
        self.root.resizable(True, True)
    
    def create_widgets(self):
        """创建主界面"""
        # 创建标签页容器
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 分割标签页
        self.segmentation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.segmentation_frame, text="图像分割")

        # 创建分割界面
        self.create_segmentation_interface(self.segmentation_frame)

        # YOLO标签页（如果可用）
        if YOLO_AVAILABLE:
            self.yolo_frame = ttk.Frame(self.notebook)
            self.notebook.add(self.yolo_frame, text="YOLO训练与识别")
            self.create_yolo_interface(self.yolo_frame)

    def create_segmentation_interface(self, parent):
        """创建分割界面"""
        # 主容器
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧面板（控制区）
        self.create_left_panel(main_frame)

        # 右侧面板（图像显示区）
        self.create_right_panel(main_frame)
    
    def create_left_panel(self, parent):
        """创建左侧控制面板"""
        left_frame = ttk.Frame(parent)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        
        # 方法选择
        self.create_method_selection(left_frame)
        
        # 文件管理
        self.create_enhanced_file_management(left_frame)
        
        # 配对状态显示
        self.create_pairing_status(left_frame)
        
        # 方法特定控制
        self.create_method_controls(left_frame)
        
        # 操作按钮
        self.create_action_buttons(left_frame)
        
        # 状态区域
        self.create_status_area(left_frame)
    
    def create_method_selection(self, parent):
        """创建方法选择区域"""
        method_frame = ttk.LabelFrame(parent, text="分割方法")
        method_frame.pack(fill=tk.X, padx=5, pady=5)

        self.method_var = tk.StringVar(value="sam")

        # SAM选项
        sam_frame = ttk.Frame(method_frame)
        sam_frame.pack(fill=tk.X, padx=5, pady=2)

        self.sam_radio = ttk.Radiobutton(sam_frame, text="SAM (自动分割)",
                                        variable=self.method_var, value="sam",
                                        command=self.on_method_change)
        self.sam_radio.pack(side=tk.LEFT)

        if not SAM_AVAILABLE:
            self.sam_radio.config(state=tk.DISABLED)
            ttk.Label(sam_frame, text="(不可用)", foreground="red").pack(side=tk.LEFT, padx=(5, 0))

        # 掩膜选项
        mask_frame = ttk.Frame(method_frame)
        mask_frame.pack(fill=tk.X, padx=5, pady=2)

        self.mask_radio = ttk.Radiobutton(mask_frame, text="掩膜分割 (精确提取)",
                                         variable=self.method_var, value="mask",
                                         command=self.on_method_change)
        self.mask_radio.pack(side=tk.LEFT)

        if not MASK_AVAILABLE:
            self.mask_radio.config(state=tk.DISABLED)
            ttk.Label(mask_frame, text="(不可用)", foreground="red").pack(side=tk.LEFT, padx=(5, 0))

        # 透明背景选项
        transparent_frame = ttk.Frame(method_frame)
        transparent_frame.pack(fill=tk.X, padx=5, pady=2)

        self.transparent_radio = ttk.Radiobutton(transparent_frame, text="透明背景分割 (PNG种子)",
                                               variable=self.method_var, value="transparent",
                                               command=self.on_method_change)
        self.transparent_radio.pack(side=tk.LEFT)

        if not TRANSPARENT_AVAILABLE:
            self.transparent_radio.config(state=tk.DISABLED)
            ttk.Label(transparent_frame, text="(不可用)", foreground="red").pack(side=tk.LEFT, padx=(5, 0))
        
        # 方法说明
        self.method_desc_var = tk.StringVar()
        self.method_desc_label = ttk.Label(method_frame, textvariable=self.method_desc_var,
                                          wraplength=400, font=('Arial', 8))
        self.method_desc_label.pack(fill=tk.X, padx=5, pady=5)
        
        # 更新说明
        self.update_method_description()
    
    def create_enhanced_file_management(self, parent):
        """创建增强的文件管理区域"""
        file_frame = ttk.LabelFrame(parent, text="文件管理")
        file_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 文件夹选择按钮
        folder_button_frame = ttk.Frame(file_frame)
        folder_button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(folder_button_frame, text="选择文件夹", 
                  command=self.select_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(folder_button_frame, text="选择单个文件", 
                  command=self.select_single_files).pack(side=tk.LEFT)
        
        # 当前文件夹信息
        self.folder_info_var = tk.StringVar(value="未选择文件夹")
        ttk.Label(file_frame, textvariable=self.folder_info_var, 
                 wraplength=400, foreground="blue").pack(fill=tk.X, padx=5, pady=2)
        
        # 文件统计信息
        self.file_stats_var = tk.StringVar(value="")
        ttk.Label(file_frame, textvariable=self.file_stats_var, 
                 font=('Arial', 8)).pack(fill=tk.X, padx=5, pady=2)
        
        # 文件列表
        list_frame = ttk.Frame(file_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        ttk.Label(list_frame, text="图像文件列表:").pack(anchor=tk.W)
        
        # 创建带滚动条的列表框
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        self.file_listbox = tk.Listbox(list_container, height=8)
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.file_listbox.bind('<<ListboxSelect>>', self.on_file_select)
        
        # 刷新按钮
        ttk.Button(list_frame, text="刷新文件列表", 
                  command=self.refresh_file_list).pack(fill=tk.X, pady=2)
    
    def create_pairing_status(self, parent):
        """创建配对状态显示区域"""
        self.pairing_frame = ttk.LabelFrame(parent, text="文件配对状态")
        self.pairing_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 配对统计
        self.pairing_stats_var = tk.StringVar(value="")
        ttk.Label(self.pairing_frame, textvariable=self.pairing_stats_var, 
                 font=('Arial', 9)).pack(fill=tk.X, padx=5, pady=2)
        
        # 未配对文件警告
        self.unpaired_warning_var = tk.StringVar(value="")
        self.unpaired_label = ttk.Label(self.pairing_frame, textvariable=self.unpaired_warning_var,
                                       foreground="orange", font=('Arial', 8))
        self.unpaired_label.pack(fill=tk.X, padx=5, pady=2)
        
        # 手动配对按钮
        self.manual_pair_button = ttk.Button(self.pairing_frame, text="手动调整配对", 
                                           command=self.open_manual_pairing_dialog)
        self.manual_pair_button.pack(fill=tk.X, padx=5, pady=2)
        
        # 初始隐藏配对状态（仅在掩膜模式下显示）
        self.pairing_frame.pack_forget()
    
    def create_method_controls(self, parent):
        """创建方法特定控制面板"""
        # 控制容器
        self.method_controls_frame = ttk.LabelFrame(parent, text="参数设置")
        self.method_controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # SAM控制
        self.sam_controls_frame = ttk.Frame(self.method_controls_frame)
        self.create_sam_controls(self.sam_controls_frame)

        # 掩膜控制
        self.mask_controls_frame = ttk.Frame(self.method_controls_frame)
        self.create_mask_controls(self.mask_controls_frame)

        # 透明背景控制
        self.transparent_controls_frame = ttk.Frame(self.method_controls_frame)
        self.create_transparent_controls(self.transparent_controls_frame)

        # 显示适当的控制
        self.update_method_controls()
    
    def create_sam_controls(self, parent):
        """创建SAM特定控制"""
        # 设备选择
        device_frame = ttk.Frame(parent)
        device_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(device_frame, text="处理设备:").pack(side=tk.LEFT)
        self.device_var = tk.StringVar(value="auto")
        device_combo = ttk.Combobox(device_frame, textvariable=self.device_var,
                                   values=["auto", "gpu", "cpu"], state="readonly", width=10)
        device_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # 每边点数
        points_frame = ttk.Frame(parent)
        points_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(points_frame, text="每边点数:").pack(side=tk.LEFT)
        self.points_per_side = tk.IntVar(value=32)
        ttk.Scale(points_frame, from_=16, to=64, variable=self.points_per_side,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 最小面积
        area_frame = ttk.Frame(parent)
        area_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(area_frame, text="最小面积:").pack(side=tk.LEFT)
        self.sam_min_area = tk.IntVar(value=100)
        ttk.Scale(area_frame, from_=50, to=1000, variable=self.sam_min_area,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    def create_mask_controls(self, parent):
        """创建掩膜特定控制"""
        # 最小种子面积
        min_area_frame = ttk.Frame(parent)
        min_area_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(min_area_frame, text="最小种子面积:").pack(side=tk.LEFT)
        self.mask_min_area = tk.IntVar(value=100)
        ttk.Scale(min_area_frame, from_=50, to=1000, variable=self.mask_min_area,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 最大种子面积
        max_area_frame = ttk.Frame(parent)
        max_area_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(max_area_frame, text="最大种子面积:").pack(side=tk.LEFT)
        self.mask_max_area = tk.IntVar(value=50000)
        ttk.Scale(max_area_frame, from_=10000, to=100000, variable=self.mask_max_area,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 边距
        padding_frame = ttk.Frame(parent)
        padding_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(padding_frame, text="裁剪边距:").pack(side=tk.LEFT)
        self.padding = tk.IntVar(value=10)
        ttk.Scale(padding_frame, from_=0, to=50, variable=self.padding,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 反转掩膜选项
        self.invert_mask = tk.BooleanVar(value=True)
        ttk.Checkbutton(parent, text="反转掩膜 (白底黑种子)",
                       variable=self.invert_mask).pack(anchor=tk.W, pady=2)

    def create_transparent_controls(self, parent):
        """创建透明背景特定控制"""
        # 最小种子面积
        min_area_frame = ttk.Frame(parent)
        min_area_frame.pack(fill=tk.X, pady=2)

        ttk.Label(min_area_frame, text="最小种子面积:").pack(side=tk.LEFT)
        self.trans_min_area = tk.IntVar(value=100)
        ttk.Scale(min_area_frame, from_=50, to=2000, variable=self.trans_min_area,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 最大种子面积
        max_area_frame = ttk.Frame(parent)
        max_area_frame.pack(fill=tk.X, pady=2)

        ttk.Label(max_area_frame, text="最大种子面积:").pack(side=tk.LEFT)
        self.trans_max_area = tk.IntVar(value=100000)
        ttk.Scale(max_area_frame, from_=10000, to=200000, variable=self.trans_max_area,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 边距
        padding_frame = ttk.Frame(parent)
        padding_frame.pack(fill=tk.X, pady=2)

        ttk.Label(padding_frame, text="裁剪边距:").pack(side=tk.LEFT)
        self.trans_padding = tk.IntVar(value=10)
        ttk.Scale(padding_frame, from_=0, to=50, variable=self.trans_padding,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Alpha阈值
        alpha_frame = ttk.Frame(parent)
        alpha_frame.pack(fill=tk.X, pady=2)

        ttk.Label(alpha_frame, text="透明度阈值:").pack(side=tk.LEFT)
        self.alpha_threshold = tk.IntVar(value=128)
        ttk.Scale(alpha_frame, from_=0, to=255, variable=self.alpha_threshold,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 噪声移除选项
        self.remove_noise = tk.BooleanVar(value=True)
        ttk.Checkbutton(parent, text="移除噪声",
                       variable=self.remove_noise).pack(anchor=tk.W, pady=2)

        # YOLO功能选项
        yolo_frame = ttk.LabelFrame(parent, text="YOLO训练功能")
        yolo_frame.pack(fill=tk.X, pady=5)

        self.generate_yolo = tk.BooleanVar(value=True)
        ttk.Checkbutton(yolo_frame, text="生成YOLO标注文件",
                       variable=self.generate_yolo).pack(anchor=tk.W, padx=5, pady=2)

        self.save_marked_crops = tk.BooleanVar(value=True)
        ttk.Checkbutton(yolo_frame, text="保存标记裁剪图片",
                       variable=self.save_marked_crops).pack(anchor=tk.W, padx=5, pady=2)

    def create_action_buttons(self, parent):
        """创建操作按钮"""
        button_frame = ttk.LabelFrame(parent, text="操作")
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        # 处理当前
        self.process_button = ttk.Button(button_frame, text="处理当前图像",
                                        command=self.process_current, state=tk.DISABLED)
        self.process_button.pack(fill=tk.X, pady=2)

        # 批量处理
        self.batch_button = ttk.Button(button_frame, text="批量处理全部",
                                      command=self.batch_process, state=tk.DISABLED)
        self.batch_button.pack(fill=tk.X, pady=2)

        # 输出目录
        output_frame = ttk.Frame(button_frame)
        output_frame.pack(fill=tk.X, pady=5)

        ttk.Label(output_frame, text="输出目录:").pack(anchor=tk.W)
        self.output_dir_var = tk.StringVar(value="output")
        ttk.Entry(output_frame, textvariable=self.output_dir_var).pack(fill=tk.X, pady=2)
        ttk.Button(output_frame, text="浏览", command=self.select_output_dir).pack(fill=tk.X, pady=2)

    def create_status_area(self, parent):
        """创建状态信息区域"""
        status_frame = ttk.LabelFrame(parent, text="状态信息")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        # 状态文本
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var, font=('Arial', 9, 'bold')).pack(anchor=tk.W, padx=5, pady=2)

        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=2)

        # 结果信息
        self.results_var = tk.StringVar(value="")
        ttk.Label(status_frame, textvariable=self.results_var, font=('Arial', 8)).pack(anchor=tk.W, padx=5, pady=2)

    def create_right_panel(self, parent):
        """创建右侧图像显示面板"""
        right_frame = ttk.Frame(parent)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 图像显示区域
        image_frame = ttk.LabelFrame(right_frame, text="图像预览")
        image_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 图像显示画布
        self.image_canvas = tk.Canvas(image_frame, bg='white')
        self.image_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 图像信息
        self.image_info_display_var = tk.StringVar(value="未加载图像")
        ttk.Label(image_frame, textvariable=self.image_info_display_var).pack(pady=2)

    def create_yolo_interface(self, parent):
        """创建YOLO训练和识别界面"""
        if not YOLO_AVAILABLE:
            ttk.Label(parent, text="YOLO功能不可用，请安装ultralytics包",
                     font=('Arial', 12)).pack(expand=True)
            return

        # 创建YOLO标签页
        yolo_notebook = ttk.Notebook(parent)
        yolo_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 模型管理标签页
        model_frame = ttk.Frame(yolo_notebook)
        yolo_notebook.add(model_frame, text="模型管理")

        # 训练标签页
        training_frame = ttk.Frame(yolo_notebook)
        yolo_notebook.add(training_frame, text="模型训练")

        # 识别标签页
        prediction_frame = ttk.Frame(yolo_notebook)
        yolo_notebook.add(prediction_frame, text="图像识别")

        # 创建YOLO组件
        self.create_yolo_components(model_frame, training_frame, prediction_frame)

    def create_yolo_components(self, model_frame, training_frame, prediction_frame):
        """创建YOLO功能组件"""
        # 初始化YOLO管理器
        self.yolo_manager = YOLOManager()

        # 模型选择器（在模型管理页面）
        self.yolo_model_selector = YOLOModelSelector(model_frame, self.yolo_manager)
        self.yolo_model_selector.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 训练面板
        self.yolo_training_panel = YOLOTrainingPanel(training_frame, self.yolo_manager)
        self.yolo_training_panel.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 预测面板
        self.yolo_prediction_panel = YOLOPredictionPanel(prediction_frame, self.yolo_manager)
        self.yolo_prediction_panel.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 设置组件间的引用
        self.yolo_training_panel.model_selector = self.yolo_model_selector
        self.yolo_prediction_panel.model_selector = self.yolo_model_selector

        # 在训练和预测面板中添加快捷按钮
        self.add_yolo_shortcuts(training_frame, prediction_frame)

    def add_yolo_shortcuts(self, training_frame, prediction_frame):
        """添加YOLO快捷功能"""
        # 训练页面快捷按钮
        training_shortcuts = ttk.Frame(training_frame)
        training_shortcuts.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(training_shortcuts, text="使用最新分割数据集",
                  command=self.use_latest_dataset).pack(side=tk.LEFT, padx=5)

        ttk.Button(training_shortcuts, text="创建YOLO数据集",
                  command=self.create_yolo_dataset_dialog).pack(side=tk.LEFT, padx=5)

        # 预测页面快捷按钮
        prediction_shortcuts = ttk.Frame(prediction_frame)
        prediction_shortcuts.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(prediction_shortcuts, text="批量预测文件夹",
                  command=self.batch_predict_folder).pack(side=tk.LEFT, padx=5)

        ttk.Button(prediction_shortcuts, text="使用训练好的模型",
                  command=self.load_trained_model).pack(side=tk.LEFT, padx=5)

    def initialize_systems(self):
        """初始化分割系统"""
        if SAM_AVAILABLE:
            try:
                sam_config = {
                    'checkpoint_path': 'sam_vit_h_4b8939.pth',
                    'device': 'auto',
                    'points_per_side': 32,
                    'min_object_area': 100,
                    'use_mask_extraction': True,
                    'generate_yolo_labels': True,
                }
                self.sam_everything = GPUAcceleratedSAMEverything(sam_config)
                self.log_message("SAM系统初始化成功")
            except Exception as e:
                self.log_message(f"SAM初始化失败: {e}")
                self.sam_everything = None

        if MASK_AVAILABLE:
            try:
                mask_config = {
                    'min_seed_area': 100,
                    'max_seed_area': 50000,
                    'padding': 10,
                    'invert_mask': True,
                }
                self.mask_segmentation = MaskBasedSegmentation(mask_config)
                self.log_message("掩膜分割系统初始化成功")
            except Exception as e:
                self.log_message(f"掩膜系统初始化失败: {e}")
                self.mask_segmentation = None

        if TRANSPARENT_AVAILABLE:
            try:
                transparent_config = {
                    'min_seed_area': 100,
                    'max_seed_area': 100000,
                    'padding': 10,
                    'alpha_threshold': 128,
                    'remove_noise': True,
                }
                self.transparent_segmentation = TransparentSeedSegmentation(transparent_config)
                self.log_message("透明背景分割系统初始化成功")
            except Exception as e:
                self.log_message(f"透明背景系统初始化失败: {e}")
                self.transparent_segmentation = None

        # 初始化YOLO管理器
        if YOLO_AVAILABLE:
            try:
                self.yolo_manager = YOLOManager()
                self.log_message("YOLO管理器初始化成功")
            except Exception as e:
                self.log_message(f"YOLO管理器初始化失败: {e}")
                self.yolo_manager = None

    def on_method_change(self):
        """处理方法选择变化"""
        self.update_method_description()
        self.update_method_controls()
        self.update_pairing_display()
        self.update_file_display()
        self.update_button_states()

    def update_method_description(self):
        """更新方法说明文本"""
        method = self.method_var.get()
        if method == "sam":
            desc = "SAM (Segment Anything Model) 使用AI自动检测和分割图像中的所有对象。"
        elif method == "mask":
            desc = "掩膜分割处理预制的掩膜图像，其中种子显示为白底上的黑色区域，提供像素级精确度。"
        else:  # transparent
            desc = "透明背景分割专门处理PNG格式图像，通过分析alpha通道自动检测和分离种子对象。"
        self.method_desc_var.set(desc)

    def update_method_controls(self):
        """显示/隐藏方法特定控制"""
        # 隐藏所有控制
        self.sam_controls_frame.pack_forget()
        self.mask_controls_frame.pack_forget()
        self.transparent_controls_frame.pack_forget()

        # 显示适当的控制
        method = self.method_var.get()
        if method == "sam":
            self.sam_controls_frame.pack(fill=tk.X, padx=5, pady=5)
        elif method == "mask":
            self.mask_controls_frame.pack(fill=tk.X, padx=5, pady=5)
        else:  # transparent
            self.transparent_controls_frame.pack(fill=tk.X, padx=5, pady=5)

    def update_pairing_display(self):
        """更新配对状态显示"""
        method = self.method_var.get()
        if method == "mask":
            self.pairing_frame.pack(fill=tk.X, padx=5, pady=5)
            self.update_pairing_status()
        else:
            self.pairing_frame.pack_forget()

    def select_folder(self):
        """选择文件夹并自动扫描图像文件"""
        folder = filedialog.askdirectory(title="选择包含图像文件的文件夹")
        if folder:
            self.current_folder = folder
            self.folder_info_var.set(f"当前文件夹: {folder}")
            self.scan_folder_for_images()
            self.auto_pair_files()
            self.update_file_display()
            self.update_pairing_status()
            self.update_button_states()

    def select_single_files(self):
        """选择单个或多个文件"""
        filetypes = [
            ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("所有文件", "*.*")
        ]

        files = filedialog.askopenfilenames(title="选择图像文件", filetypes=filetypes)
        if files:
            self.image_files = list(files)
            self.current_folder = os.path.dirname(files[0]) if files else None

            # 如果是掩膜模式，也扫描掩膜文件
            if self.method_var.get() == "mask" and self.current_folder:
                self.scan_folder_for_masks()
                self.auto_pair_files()

            self.update_file_stats()
            self.update_file_display()
            self.update_pairing_status()
            self.update_button_states()

    def scan_folder_for_images(self):
        """扫描文件夹中的所有图像文件"""
        if not self.current_folder:
            return

        self.image_files = []
        self.mask_files = []

        # 扫描所有支持的图像格式
        for ext in self.supported_formats:
            # 原始图像文件
            pattern = os.path.join(self.current_folder, f"*{ext}")
            files = glob.glob(pattern, recursive=False)

            for file in files:
                filename = os.path.basename(file).lower()
                # 区分掩膜文件和原始图像文件
                if any(keyword in filename for keyword in ['mask', '掩膜', '_m.', '_mask.']):
                    self.mask_files.append(file)
                else:
                    self.image_files.append(file)

        # 排序文件列表
        self.image_files.sort()
        self.mask_files.sort()

        self.update_file_stats()

    def scan_folder_for_masks(self):
        """专门扫描掩膜文件"""
        if not self.current_folder:
            return

        self.mask_files = []
        for ext in self.supported_formats:
            pattern = os.path.join(self.current_folder, f"*{ext}")
            files = glob.glob(pattern, recursive=False)

            for file in files:
                filename = os.path.basename(file).lower()
                if any(keyword in filename for keyword in ['mask', '掩膜', '_m.', '_mask.']):
                    self.mask_files.append(file)

        self.mask_files.sort()

    def update_file_stats(self):
        """更新文件统计信息"""
        method = self.method_var.get()
        if method == "sam":
            stats = f"图像文件: {len(self.image_files)} 个"
        else:
            stats = f"图像文件: {len(self.image_files)} 个, 掩膜文件: {len(self.mask_files)} 个"

        self.file_stats_var.set(stats)

    def auto_pair_files(self):
        """自动配对掩膜文件和原始图像文件"""
        self.paired_files = {}
        self.unpaired_files = []

        if self.method_var.get() != "mask":
            return

        # 创建掩膜文件的基础名称映射
        mask_dict = {}
        for mask_file in self.mask_files:
            base_name = self.extract_base_name(mask_file)
            mask_dict[base_name] = mask_file

        # 为每个原始图像文件寻找匹配的掩膜
        for image_file in self.image_files:
            base_name = self.extract_base_name(image_file)

            # 尝试多种匹配模式
            matched_mask = None

            # 1. 精确匹配
            if base_name in mask_dict:
                matched_mask = mask_dict[base_name]

            # 2. 带_mask后缀的匹配
            elif f"{base_name}_mask" in mask_dict:
                matched_mask = mask_dict[f"{base_name}_mask"]

            # 3. 带mask后缀的匹配
            elif f"{base_name}mask" in mask_dict:
                matched_mask = mask_dict[f"{base_name}mask"]

            # 4. 反向匹配（掩膜文件可能没有后缀）
            else:
                for mask_base, mask_path in mask_dict.items():
                    if mask_base.startswith(base_name) or base_name.startswith(mask_base):
                        matched_mask = mask_path
                        break

            if matched_mask:
                self.paired_files[image_file] = matched_mask
            else:
                self.unpaired_files.append(image_file)

    def extract_base_name(self, file_path):
        """提取文件的基础名称（去除扩展名和常见后缀）"""
        base_name = os.path.splitext(os.path.basename(file_path))[0]

        # 移除常见的掩膜后缀
        suffixes_to_remove = ['_mask', '_m', 'mask', '掩膜']
        for suffix in suffixes_to_remove:
            if base_name.lower().endswith(suffix.lower()):
                base_name = base_name[:-len(suffix)]
                break

        return base_name

    def update_pairing_status(self):
        """更新配对状态信息"""
        if self.method_var.get() != "mask":
            return

        paired_count = len(self.paired_files)
        unpaired_count = len(self.unpaired_files)
        total_images = len(self.image_files)

        if total_images == 0:
            self.pairing_stats_var.set("无图像文件")
            self.unpaired_warning_var.set("")
            return

        # 配对统计
        stats = f"已配对: {paired_count}/{total_images} 个文件"
        self.pairing_stats_var.set(stats)

        # 未配对警告
        if unpaired_count > 0:
            warning = f"警告: {unpaired_count} 个文件未找到匹配的掩膜"
            self.unpaired_warning_var.set(warning)
        else:
            self.unpaired_warning_var.set("✓ 所有文件已成功配对")

    def update_file_display(self):
        """更新文件列表显示"""
        self.file_listbox.delete(0, tk.END)

        method = self.method_var.get()
        if method == "sam":
            # SAM模式：显示图像文件
            for img_file in self.image_files:
                display_name = os.path.basename(img_file)
                self.file_listbox.insert(tk.END, display_name)
        else:
            # 掩膜模式：显示配对状态
            for img_file in self.image_files:
                img_name = os.path.basename(img_file)
                if img_file in self.paired_files:
                    mask_name = os.path.basename(self.paired_files[img_file])
                    display_name = f"✓ {img_name} ← {mask_name}"
                else:
                    display_name = f"✗ {img_name} (无掩膜)"

                self.file_listbox.insert(tk.END, display_name)

    def refresh_file_list(self):
        """刷新文件列表"""
        if self.current_folder:
            self.scan_folder_for_images()
            self.auto_pair_files()
            self.update_file_display()
            self.update_pairing_status()
            self.update_button_states()
            self.log_message("文件列表已刷新")

    def open_manual_pairing_dialog(self):
        """打开手动配对对话框"""
        if not self.unpaired_files:
            messagebox.showinfo("信息", "所有文件已成功配对，无需手动调整。")
            return

        # 创建手动配对窗口
        dialog = tk.Toplevel(self.root)
        dialog.title("手动调整文件配对")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 未配对文件列表
        ttk.Label(dialog, text="未配对的图像文件:").pack(anchor=tk.W, padx=10, pady=5)

        unpaired_frame = ttk.Frame(dialog)
        unpaired_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        unpaired_listbox = tk.Listbox(unpaired_frame)
        unpaired_scrollbar = ttk.Scrollbar(unpaired_frame, orient=tk.VERTICAL, command=unpaired_listbox.yview)
        unpaired_listbox.configure(yscrollcommand=unpaired_scrollbar.set)

        unpaired_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        unpaired_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充未配对文件
        for file in self.unpaired_files:
            unpaired_listbox.insert(tk.END, os.path.basename(file))

        # 操作按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        def select_mask_for_image():
            selection = unpaired_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请先选择一个图像文件。")
                return

            image_file = self.unpaired_files[selection[0]]

            # 选择掩膜文件
            filetypes = [("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif")]
            mask_file = filedialog.askopenfilename(
                title=f"为 {os.path.basename(image_file)} 选择掩膜文件",
                filetypes=filetypes,
                initialdir=self.current_folder
            )

            if mask_file:
                # 添加配对
                self.paired_files[image_file] = mask_file
                self.unpaired_files.remove(image_file)

                # 更新列表
                unpaired_listbox.delete(selection[0])

                # 更新主界面
                self.update_file_display()
                self.update_pairing_status()

                messagebox.showinfo("成功", f"已为 {os.path.basename(image_file)} 配对掩膜文件。")

        ttk.Button(button_frame, text="选择掩膜文件", command=select_mask_for_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

    def update_button_states(self):
        """更新按钮状态"""
        method = self.method_var.get()
        has_files = len(self.image_files) > 0

        if method == "sam":
            can_process = has_files and self.sam_everything is not None
        elif method == "mask":
            can_process = len(self.paired_files) > 0 and self.mask_segmentation is not None
        else:  # transparent
            can_process = has_files and self.transparent_segmentation is not None

        state = tk.NORMAL if can_process and not self.processing else tk.DISABLED
        self.process_button.config(state=state)
        self.batch_button.config(state=state)

    def on_file_select(self, event):
        """处理文件列表选择事件"""
        selection = self.file_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        method = self.method_var.get()

        if method == "sam":
            if index < len(self.image_files):
                self.load_and_display_image(self.image_files[index])
        else:
            if index < len(self.image_files):
                img_file = self.image_files[index]
                self.load_and_display_image(img_file)

                # 设置当前掩膜路径
                if img_file in self.paired_files:
                    self.current_mask_path = self.paired_files[img_file]
                else:
                    self.current_mask_path = None

    def load_and_display_image(self, image_path):
        """加载并显示图像"""
        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                self.log_message(f"无法加载图像: {image_path}")
                return

            self.current_image = image
            self.current_image_path = image_path

            # 转换为显示格式
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 调整大小以适应画布
            canvas_width = self.image_canvas.winfo_width()
            canvas_height = self.image_canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                h, w = image_rgb.shape[:2]
                scale = min(canvas_width / w, canvas_height / h, 1.0)

                if scale < 1.0:
                    new_w = int(w * scale)
                    new_h = int(h * scale)
                    image_rgb = cv2.resize(image_rgb, (new_w, new_h))

                # 转换为PhotoImage
                pil_image = Image.fromarray(image_rgb)
                self.photo = ImageTk.PhotoImage(pil_image)

                # 在画布上显示
                self.image_canvas.delete("all")
                self.image_canvas.create_image(canvas_width//2, canvas_height//2,
                                             image=self.photo, anchor=tk.CENTER)

            # 更新信息
            h, w = image.shape[:2]
            filename = os.path.basename(image_path)
            self.image_info_display_var.set(f"{filename} - {w}x{h}")

        except Exception as e:
            self.log_message(f"加载图像时出错: {e}")

    def select_output_dir(self):
        """选择输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir_var.set(directory)

    def process_current(self):
        """处理当前选中的图像"""
        if self.processing:
            return

        method = self.method_var.get()

        if method == "sam":
            if not self.current_image_path or self.sam_everything is None:
                messagebox.showerror("错误", "未选择图像或SAM系统不可用")
                return

            # 在线程中处理SAM
            threading.Thread(target=self._process_sam_single, daemon=True).start()

        elif method == "mask":
            if not self.current_image_path or not self.current_mask_path or self.mask_segmentation is None:
                messagebox.showerror("错误", "必须选择图像和掩膜文件，或掩膜系统不可用")
                return

            # 在线程中处理掩膜分割
            threading.Thread(target=self._process_mask_single, daemon=True).start()

        else:  # transparent
            if not self.current_image_path or self.transparent_segmentation is None:
                messagebox.showerror("错误", "未选择PNG图像或透明背景系统不可用")
                return

            # 在线程中处理透明背景分割
            threading.Thread(target=self._process_transparent_single, daemon=True).start()

    def batch_process(self):
        """批量处理所有文件"""
        if self.processing:
            return

        method = self.method_var.get()

        if method == "sam":
            if not self.image_files or self.sam_everything is None:
                messagebox.showerror("错误", "未选择图像或SAM系统不可用")
                return

            threading.Thread(target=self._batch_process_sam, daemon=True).start()

        elif method == "mask":
            if not self.paired_files or self.mask_segmentation is None:
                messagebox.showerror("错误", "无配对文件或掩膜系统不可用")
                return

            threading.Thread(target=self._batch_process_mask, daemon=True).start()

        else:  # transparent
            if not self.image_files or self.transparent_segmentation is None:
                messagebox.showerror("错误", "未选择PNG图像或透明背景系统不可用")
                return

            threading.Thread(target=self._batch_process_transparent, daemon=True).start()

    def _process_sam_single(self):
        """单个SAM处理（在线程中运行）"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())
        self.root.after(0, lambda: self.status_var.set("正在使用SAM处理..."))

        try:
            # 更新SAM配置
            config = {
                'checkpoint_path': 'sam_vit_h_4b8939.pth',
                'device': self.device_var.get(),
                'points_per_side': self.points_per_side.get(),
                'min_object_area': self.sam_min_area.get(),
                'use_mask_extraction': True,
                'generate_yolo_labels': True,
            }

            # 如果需要重新初始化
            if self.sam_everything is None:
                self.sam_everything = GPUAcceleratedSAMEverything(config)

            # 创建输出目录
            output_dir = self.output_dir_var.get()
            base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
            image_output_dir = os.path.join(output_dir, base_name)

            # 处理
            result = self.sam_everything.process_image(self.current_image, self.current_image_path, image_output_dir)

            # 更新界面
            if result['success']:
                self.root.after(0, lambda: self.status_var.set("SAM处理完成"))
                self.root.after(0, lambda: self.results_var.set(
                    f"检测对象: {result['objects_count']}, 用时: {result['processing_time']:.2f}秒"
                ))
                self.root.after(0, lambda: messagebox.showinfo("成功",
                    f"处理完成！\n检测对象: {result['objects_count']}\n输出目录: {image_output_dir}"))
            else:
                self.root.after(0, lambda: self.status_var.set("SAM处理失败"))
                self.root.after(0, lambda: messagebox.showerror("错误", f"处理失败: {result.get('error', '未知错误')}"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set("SAM处理出错"))
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理出错: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def _process_mask_single(self):
        """单个掩膜处理（在线程中运行）"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())
        self.root.after(0, lambda: self.status_var.set("正在使用掩膜分割处理..."))

        try:
            # 更新掩膜配置
            config = {
                'min_seed_area': self.mask_min_area.get(),
                'max_seed_area': self.mask_max_area.get(),
                'padding': self.padding.get(),
                'invert_mask': self.invert_mask.get(),
                'output_format': 'png',
            }

            # 如果需要重新初始化
            if self.mask_segmentation is None:
                self.mask_segmentation = MaskBasedSegmentation(config)

            # 创建输出目录
            output_dir = self.output_dir_var.get()
            base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
            image_output_dir = os.path.join(output_dir, base_name)

            # 处理
            result = self.mask_segmentation.process_mask_and_original(
                self.current_mask_path, self.current_image_path, image_output_dir
            )

            # 更新界面
            if result['success']:
                self.root.after(0, lambda: self.status_var.set("掩膜分割处理完成"))
                self.root.after(0, lambda: self.results_var.set(
                    f"提取种子: {result['seeds_count']}, 比例尺: {result['scale_bars_count']}, 用时: {result['processing_time']:.2f}秒"
                ))
                self.root.after(0, lambda: messagebox.showinfo("成功",
                    f"处理完成！\n提取种子: {result['seeds_count']}\n检测比例尺: {result['scale_bars_count']}\n输出目录: {image_output_dir}"))
            else:
                self.root.after(0, lambda: self.status_var.set("掩膜分割处理失败"))
                self.root.after(0, lambda: messagebox.showerror("错误", f"处理失败: {result.get('error', '未知错误')}"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set("掩膜分割处理出错"))
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理出错: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def _batch_process_sam(self):
        """批量SAM处理（在线程中运行）"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())

        try:
            output_dir = self.output_dir_var.get()
            total_files = len(self.image_files)
            successful = 0

            for i, image_path in enumerate(self.image_files):
                self.root.after(0, lambda i=i, total=total_files: self.status_var.set(f"正在处理 {i+1}/{total}..."))

                # 加载图像
                image = cv2.imread(image_path)
                if image is None:
                    continue

                # 创建输出目录
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                image_output_dir = os.path.join(output_dir, base_name)

                # 处理
                result = self.sam_everything.process_image(image, image_path, image_output_dir)
                if result['success']:
                    successful += 1

            # 显示结果
            self.root.after(0, lambda: self.status_var.set("批量处理完成"))
            self.root.after(0, lambda: self.results_var.set(f"已处理: {successful}/{total_files}"))
            self.root.after(0, lambda: messagebox.showinfo("批量处理完成",
                f"批量处理完成！\n成功: {successful}/{total_files}"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"批量处理出错: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def _batch_process_mask(self):
        """批量掩膜处理（在线程中运行）"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())

        try:
            output_dir = self.output_dir_var.get()
            paired_items = list(self.paired_files.items())
            total_pairs = len(paired_items)
            successful = 0
            total_seeds = 0

            for i, (image_path, mask_path) in enumerate(paired_items):
                self.root.after(0, lambda i=i, total=total_pairs: self.status_var.set(f"正在处理配对 {i+1}/{total}..."))

                # 处理
                result = self.mask_segmentation.process_mask_and_original(
                    mask_path, image_path, output_dir
                )

                if result['success']:
                    successful += 1
                    total_seeds += result['seeds_count']

            # 显示结果
            self.root.after(0, lambda: self.status_var.set("批量处理完成"))
            self.root.after(0, lambda: self.results_var.set(f"已处理: {successful}/{total_pairs}, 种子: {total_seeds}"))
            self.root.after(0, lambda: messagebox.showinfo("批量处理完成",
                f"批量处理完成！\n成功: {successful}/{total_pairs}\n总种子数: {total_seeds}"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"批量处理出错: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def _process_transparent_single(self):
        """单个透明背景处理（在线程中运行）"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())
        self.root.after(0, lambda: self.status_var.set("正在使用透明背景分割处理..."))

        try:
            # 更新透明背景配置
            config = {
                'min_seed_area': self.trans_min_area.get(),
                'max_seed_area': self.trans_max_area.get(),
                'padding': self.trans_padding.get(),
                'alpha_threshold': self.alpha_threshold.get(),
                'remove_noise': self.remove_noise.get(),
                'output_format': 'png',
                'preserve_quality': True,
            }

            # 如果需要重新初始化
            if self.transparent_segmentation is None:
                self.transparent_segmentation = TransparentSeedSegmentation(config)

            # 创建输出目录
            output_dir = self.output_dir_var.get()
            base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
            image_output_dir = os.path.join(output_dir, base_name)

            # 处理
            result = self.transparent_segmentation.process_transparent_image(
                self.current_image_path, image_output_dir
            )

            # 更新界面
            if result['success']:
                self.root.after(0, lambda: self.status_var.set("透明背景分割处理完成"))
                self.root.after(0, lambda: self.results_var.set(
                    f"提取种子: {result['seeds_count']}, 用时: {result['processing_time']:.2f}秒"
                ))
                self.root.after(0, lambda: messagebox.showinfo("成功",
                    f"处理完成！\n提取种子: {result['seeds_count']}\n非透明像素: {result.get('non_transparent_pixels', 'N/A')}\n输出目录: {image_output_dir}"))
            else:
                self.root.after(0, lambda: self.status_var.set("透明背景分割处理失败"))
                self.root.after(0, lambda: messagebox.showerror("错误", f"处理失败: {result.get('error', '未知错误')}"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set("透明背景分割处理出错"))
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理出错: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def _batch_process_transparent(self):
        """批量透明背景处理（在线程中运行）"""
        self.processing = True
        self.root.after(0, lambda: self.progress_bar.start())

        try:
            output_dir = self.output_dir_var.get()
            total_files = len(self.image_files)
            successful = 0
            total_seeds = 0

            for i, image_path in enumerate(self.image_files):
                self.root.after(0, lambda i=i, total=total_files: self.status_var.set(f"正在处理 {i+1}/{total}..."))

                # 创建输出目录
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                image_output_dir = os.path.join(output_dir, base_name)

                # 处理
                result = self.transparent_segmentation.process_transparent_image(image_path, image_output_dir)

                if result['success']:
                    successful += 1
                    total_seeds += result['seeds_count']

            # 显示结果
            self.root.after(0, lambda: self.status_var.set("批量处理完成"))
            self.root.after(0, lambda: self.results_var.set(f"已处理: {successful}/{total_files}, 种子: {total_seeds}"))
            self.root.after(0, lambda: messagebox.showinfo("批量处理完成",
                f"批量处理完成！\n成功: {successful}/{total_files}\n总种子数: {total_seeds}"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"批量处理出错: {str(e)}"))

        finally:
            self.processing = False
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, self.update_button_states)

    def log_message(self, message):
        """记录消息到状态栏"""
        print(f"[GUI] {message}")
        self.status_var.set(message)

    # YOLO相关方法
    def use_latest_dataset(self):
        """使用最新的分割数据集进行训练"""
        if not YOLO_AVAILABLE or not self.yolo_manager:
            messagebox.showerror("错误", "YOLO功能不可用")
            return

        # 查找最新的数据集配置文件
        output_dir = self.output_dir_var.get()
        dataset_files = []

        for root, dirs, files in os.walk(output_dir):
            for file in files:
                if file == "dataset.yaml":
                    dataset_files.append(os.path.join(root, file))

        if not dataset_files:
            messagebox.showwarning("警告", "未找到数据集配置文件，请先进行图像分割并创建YOLO数据集")
            return

        # 选择最新的数据集文件
        latest_dataset = max(dataset_files, key=os.path.getmtime)

        # 设置到训练面板
        if hasattr(self, 'yolo_training_panel'):
            self.yolo_training_panel.dataset_path.set(latest_dataset)
            messagebox.showinfo("成功", f"已设置数据集: {os.path.basename(latest_dataset)}")

    def create_yolo_dataset_dialog(self):
        """创建YOLO数据集对话框"""
        if not YOLO_AVAILABLE:
            messagebox.showerror("错误", "YOLO功能不可用")
            return

        # 选择源目录
        source_dir = filedialog.askdirectory(title="选择分割结果目录")
        if not source_dir:
            return

        # 选择输出目录
        output_dir = filedialog.askdirectory(title="选择YOLO数据集输出目录")
        if not output_dir:
            return

        # 创建数据集
        def create_dataset():
            try:
                from yolo_format_converter import YOLOFormatConverter
                converter = YOLOFormatConverter()

                dataset_info = converter.create_yolo_dataset(source_dir, output_dir, 0.8)

                if dataset_info:
                    self.root.after(0, lambda: messagebox.showinfo("成功",
                        f"YOLO数据集创建成功！\n训练图像: {dataset_info['train_images']}\n验证图像: {dataset_info['val_images']}"))

                    # 自动设置到训练面板
                    if hasattr(self, 'yolo_training_panel'):
                        self.root.after(0, lambda: self.yolo_training_panel.dataset_path.set(dataset_info['config_file']))
                else:
                    self.root.after(0, lambda: messagebox.showerror("错误", "数据集创建失败"))

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"创建数据集时出错: {e}"))

        threading.Thread(target=create_dataset, daemon=True).start()

    def batch_predict_folder(self):
        """批量预测文件夹中的图像"""
        if not YOLO_AVAILABLE or not hasattr(self, 'yolo_model_selector'):
            messagebox.showerror("错误", "YOLO功能不可用")
            return

        model_path = self.yolo_model_selector.get_selected_model_path()
        if not model_path:
            messagebox.showerror("错误", "请先选择并下载一个模型")
            return

        # 选择图像文件夹
        image_dir = filedialog.askdirectory(title="选择要预测的图像文件夹")
        if not image_dir:
            return

        # 选择输出目录
        output_dir = filedialog.askdirectory(title="选择预测结果输出目录")
        if not output_dir:
            return

        # 批量预测
        def batch_predict():
            try:
                import glob
                image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
                image_files = []

                for ext in image_extensions:
                    image_files.extend(glob.glob(os.path.join(image_dir, ext)))
                    image_files.extend(glob.glob(os.path.join(image_dir, ext.upper())))

                if not image_files:
                    self.root.after(0, lambda: messagebox.showwarning("警告", "未找到图像文件"))
                    return

                total_files = len(image_files)
                successful = 0

                for i, image_file in enumerate(image_files):
                    self.root.after(0, lambda i=i, total=total_files:
                                   self.status_var.set(f"正在预测 {i+1}/{total}..."))

                    result = self.yolo_manager.predict_image(
                        model_path=model_path,
                        image_path=image_file,
                        confidence=0.5,
                        save_dir=output_dir
                    )

                    if result:
                        successful += 1

                self.root.after(0, lambda: messagebox.showinfo("完成",
                    f"批量预测完成！\n成功: {successful}/{total_files}\n结果保存在: {output_dir}"))

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"批量预测失败: {e}"))

        threading.Thread(target=batch_predict, daemon=True).start()

    def load_trained_model(self):
        """加载训练好的模型"""
        if not YOLO_AVAILABLE or not self.yolo_manager:
            messagebox.showerror("错误", "YOLO功能不可用")
            return

        # 获取已训练的模型列表
        trained_models = self.yolo_manager.list_trained_models()

        if not trained_models:
            messagebox.showinfo("信息", "未找到训练好的模型")
            return

        # 创建选择对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("选择训练好的模型")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 模型列表
        ttk.Label(dialog, text="选择要使用的训练模型:").pack(pady=10)

        listbox = tk.Listbox(dialog, height=15)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for model_path in trained_models:
            model_name = os.path.basename(model_path)
            listbox.insert(tk.END, model_name)

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def use_selected_model():
            selection = listbox.curselection()
            if selection:
                selected_model = trained_models[selection[0]]

                # 这里可以添加将模型设置到预测面板的逻辑
                messagebox.showinfo("成功", f"已选择模型: {os.path.basename(selected_model)}")
                dialog.destroy()
            else:
                messagebox.showwarning("警告", "请选择一个模型")

        ttk.Button(button_frame, text="使用选中模型", command=use_selected_model).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

def main():
    """主函数运行GUI"""
    root = tk.Tk()
    app = EnhancedSegmentationGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
