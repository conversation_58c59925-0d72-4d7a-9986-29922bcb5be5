#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本GUI测试 - 验证小屏幕适配功能
"""

import tkinter as tk
from tkinter import ttk
import sys

def test_basic_gui():
    """测试基本的GUI组件和布局"""
    
    root = tk.Tk()
    root.title("小屏幕适配测试")
    
    # 自适应窗口大小
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    window_width = min(1400, int(screen_width * 0.85))
    window_height = min(900, int(screen_height * 0.85))
    
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    
    root.geometry(f"{window_width}x{window_height}+{x}+{y}")
    root.minsize(1200, 700)
    
    # 主框架
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 左侧滚动区域
    left_container = ttk.Frame(main_frame)
    left_container.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
    
    # 创建Canvas和滚动条
    left_canvas = tk.Canvas(left_container, width=420, highlightthickness=0)
    left_scrollbar = ttk.Scrollbar(left_container, orient=tk.VERTICAL, command=left_canvas.yview)
    scrollable_left_frame = ttk.Frame(left_canvas)
    
    # 配置滚动
    scrollable_left_frame.bind(
        "<Configure>",
        lambda e: left_canvas.configure(scrollregion=left_canvas.bbox("all"))
    )
    
    left_canvas.create_window((0, 0), window=scrollable_left_frame, anchor="nw")
    left_canvas.configure(yscrollcommand=left_scrollbar.set)
    
    # 布局Canvas和滚动条
    left_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    left_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 绑定鼠标滚轮事件
    def _on_mousewheel(event):
        left_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def _bind_to_mousewheel(event):
        left_canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def _unbind_from_mousewheel(event):
        left_canvas.unbind_all("<MouseWheel>")
    
    left_canvas.bind('<Enter>', _bind_to_mousewheel)
    left_canvas.bind('<Leave>', _unbind_from_mousewheel)
    
    # 在滚动框架中添加测试内容
    create_test_controls(scrollable_left_frame)
    
    # 右侧图像显示区域
    image_frame = ttk.LabelFrame(main_frame, text="图像显示区域")
    image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
    
    canvas_img = tk.Canvas(image_frame, bg='lightgray')
    canvas_img.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 显示测试信息
    canvas_img.create_text(
        300, 200, 
        text=f"小屏幕适配测试\n\n"
             f"窗口大小: {window_width}x{window_height}\n"
             f"屏幕分辨率: {screen_width}x{screen_height}\n\n"
             f"✅ 自适应窗口大小\n"
             f"✅ 滚动支持\n"
             f"✅ 可折叠控件\n"
             f"✅ 鼠标滚轮支持\n\n"
             f"请滚动左侧面板测试所有功能",
        font=('Arial', 12),
        fill='black',
        anchor='center'
    )
    
    root.mainloop()

def create_test_controls(parent):
    """创建测试控件"""
    
    # 设备选择区域
    device_frame = ttk.LabelFrame(parent, text="设备选择")
    device_frame.pack(fill=tk.X, padx=5, pady=5)
    
    device_var = tk.StringVar(value="auto")
    for text, value in [("自动选择", "auto"), ("强制使用GPU", "gpu"), ("强制使用CPU", "cpu")]:
        ttk.Radiobutton(device_frame, text=text, variable=device_var, value=value).pack(anchor=tk.W, padx=5)
    
    # 文件管理区域
    file_frame = ttk.LabelFrame(parent, text="文件管理")
    file_frame.pack(fill=tk.X, padx=5, pady=5)
    
    folder_frame = ttk.Frame(file_frame)
    folder_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Button(folder_frame, text="选择文件夹").pack(side=tk.LEFT, padx=(0, 5))
    ttk.Button(folder_frame, text="选择单个文件").pack(side=tk.LEFT)
    
    # 操作按钮区域
    button_frame = ttk.LabelFrame(parent, text="操作")
    button_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Button(button_frame, text="处理当前图像").pack(fill=tk.X, pady=2)
    ttk.Button(button_frame, text="批量处理所有图像").pack(fill=tk.X, pady=2)
    
    # 可折叠参数控制
    param_main_frame = ttk.Frame(parent)
    param_main_frame.pack(fill=tk.X, padx=5, pady=5)
    
    param_collapsed = tk.BooleanVar(value=True)
    param_header = ttk.Frame(param_main_frame)
    param_header.pack(fill=tk.X)
    
    def toggle_parameters():
        if param_collapsed.get():
            param_content_frame.pack(fill=tk.X, pady=(5, 0))
            param_toggle_btn.config(text="▼ 高级参数设置")
            param_collapsed.set(False)
        else:
            param_content_frame.pack_forget()
            param_toggle_btn.config(text="▶ 高级参数设置")
            param_collapsed.set(True)
    
    param_toggle_btn = ttk.Button(param_header, text="▶ 高级参数设置", command=toggle_parameters)
    param_toggle_btn.pack(side=tk.LEFT)
    
    param_content_frame = ttk.Frame(param_main_frame)
    
    # 参数控件
    sam_frame = ttk.LabelFrame(param_content_frame, text="SAM参数")
    sam_frame.pack(fill=tk.X, padx=5, pady=5)
    
    for i, param_name in enumerate(["每边点数", "IoU阈值", "稳定性阈值", "最小面积"]):
        param_frame = ttk.Frame(sam_frame)
        param_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(param_frame, text=f"{param_name}:", width=12).pack(side=tk.LEFT)
        tk.Scale(param_frame, from_=0, to=100, orient=tk.HORIZONTAL, length=250).pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    # YOLO设置
    yolo_frame = ttk.LabelFrame(parent, text="YOLO标签设置")
    yolo_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Checkbutton(yolo_frame, text="生成YOLO标签文件").pack(anchor=tk.W, padx=5)
    
    class_frame = ttk.Frame(yolo_frame)
    class_frame.pack(fill=tk.X, padx=5, pady=2)
    ttk.Label(class_frame, text="默认类别ID:").pack(side=tk.LEFT)
    ttk.Spinbox(class_frame, from_=0, to=999, width=10).pack(side=tk.LEFT, padx=(5, 0))
    
    # 状态信息区域
    status_frame = ttk.LabelFrame(parent, text="状态信息")
    status_frame.pack(fill=tk.X, padx=5, pady=5)
    
    # 进度显示
    progress_frame = ttk.Frame(status_frame)
    progress_frame.pack(fill=tk.X, padx=5, pady=2)
    
    ttk.Label(progress_frame, text="当前进度:", font=('Arial', 8)).pack(side=tk.LEFT)
    ttk.Progressbar(progress_frame, mode='indeterminate', length=200).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
    ttk.Label(progress_frame, text="0%", font=('Arial', 8), width=8).pack(side=tk.RIGHT)
    
    # 统计信息
    stats_frame = ttk.Frame(status_frame)
    stats_frame.pack(fill=tk.X, padx=5, pady=2)
    
    ttk.Label(stats_frame, text="处理时间:", font=('Arial', 8)).pack(side=tk.LEFT)
    ttk.Label(stats_frame, text="--", font=('Arial', 8), foreground="green").pack(side=tk.LEFT, padx=(5, 15))
    
    ttk.Label(stats_frame, text="检测对象:", font=('Arial', 8)).pack(side=tk.LEFT)
    ttk.Label(stats_frame, text="--", font=('Arial', 8), foreground="blue").pack(side=tk.LEFT, padx=(5, 0))
    
    # 日志区域
    log_frame = ttk.Frame(status_frame)
    log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)
    
    ttk.Label(log_frame, text="处理日志:", font=('Arial', 8)).pack(anchor=tk.W)
    
    log_text = tk.Text(log_frame, height=3, wrap=tk.WORD, font=('Arial', 8))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 添加一些测试日志
    log_text.insert(tk.END, "✅ GUI界面初始化完成\n")
    log_text.insert(tk.END, "🔍 检查GPU状态中...\n")
    log_text.insert(tk.END, "📁 请选择图像文件或文件夹\n")

if __name__ == "__main__":
    test_basic_gui()
