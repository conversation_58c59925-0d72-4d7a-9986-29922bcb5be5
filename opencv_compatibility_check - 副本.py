#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV兼容性检查和修复工具
检查OpenCV版本并提供兼容性解决方案
"""

import cv2
import numpy as np
import sys

def check_opencv_version():
    """检查OpenCV版本信息"""
    print("OpenCV版本信息:")
    print(f"  版本: {cv2.__version__}")
    
    # 解析版本号
    version_parts = cv2.__version__.split('.')
    major = int(version_parts[0])
    minor = int(version_parts[1]) if len(version_parts) > 1 else 0
    patch = int(version_parts[2]) if len(version_parts) > 2 else 0
    
    print(f"  主版本: {major}")
    print(f"  次版本: {minor}")
    print(f"  补丁版本: {patch}")
    
    return major, minor, patch

def test_connected_components():
    """测试connectedComponentsWithStats函数的兼容性"""
    print("\n测试connectedComponentsWithStats兼容性:")
    
    # 创建测试图像
    test_image = np.zeros((100, 100), dtype=np.uint8)
    cv2.circle(test_image, (30, 30), 15, 255, -1)
    cv2.circle(test_image, (70, 70), 20, 255, -1)
    
    methods = [
        ("新版本 (ltype参数)", lambda img: cv2.connectedComponentsWithStats(img, connectivity=8, ltype=cv2.CV_32S)),
        ("中版本 (位置参数)", lambda img: cv2.connectedComponentsWithStats(img, 8, cv2.CV_32S)),
        ("老版本 (基本参数)", lambda img: cv2.connectedComponentsWithStats(img, 8)),
        ("最简版本", lambda img: cv2.connectedComponentsWithStats(img)),
    ]
    
    working_method = None
    
    for method_name, method_func in methods:
        try:
            result = method_func(test_image)
            if len(result) == 4:  # 正确的返回值数量
                num_labels, labels, stats, centroids = result
                print(f"  ✅ {method_name}: 工作正常 (找到 {num_labels-1} 个组件)")
                if working_method is None:
                    working_method = (method_name, method_func)
            else:
                print(f"  ❌ {method_name}: 返回值数量错误")
        except Exception as e:
            print(f"  ❌ {method_name}: {str(e)}")
    
    return working_method

def create_compatible_function():
    """创建兼容的connectedComponentsWithStats函数"""
    working_method = test_connected_components()
    
    if working_method is None:
        print("\n❌ 无法找到工作的connectedComponentsWithStats方法!")
        return None
    
    method_name, method_func = working_method
    print(f"\n✅ 推荐使用方法: {method_name}")
    
    # 生成兼容代码
    if "ltype" in method_name:
        code = """
def safe_connected_components(image, connectivity=8):
    return cv2.connectedComponentsWithStats(image, connectivity=connectivity, ltype=cv2.CV_32S)
"""
    elif "位置参数" in method_name:
        code = """
def safe_connected_components(image, connectivity=8):
    return cv2.connectedComponentsWithStats(image, connectivity, cv2.CV_32S)
"""
    elif "基本参数" in method_name:
        code = """
def safe_connected_components(image, connectivity=8):
    return cv2.connectedComponentsWithStats(image, connectivity)
"""
    else:
        code = """
def safe_connected_components(image, connectivity=8):
    return cv2.connectedComponentsWithStats(image)
"""
    
    print("\n推荐的兼容代码:")
    print(code)
    
    return code

def test_image_io():
    """测试图像读写功能"""
    print("\n测试图像I/O功能:")
    
    # 测试基本读写
    try:
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        success = cv2.imwrite("test_opencv.jpg", test_image)
        if success:
            loaded = cv2.imread("test_opencv.jpg")
            if loaded is not None:
                print("  ✅ 基本图像读写: 正常")
            else:
                print("  ❌ 基本图像读写: 读取失败")
        else:
            print("  ❌ 基本图像读写: 写入失败")
        
        # 清理测试文件
        import os
        if os.path.exists("test_opencv.jpg"):
            os.remove("test_opencv.jpg")
            
    except Exception as e:
        print(f"  ❌ 基本图像读写: {e}")
    
    # 测试PNG透明度支持
    try:
        test_rgba = np.random.randint(0, 255, (50, 50, 4), dtype=np.uint8)
        success = cv2.imwrite("test_alpha.png", test_rgba)
        if success:
            loaded = cv2.imread("test_alpha.png", cv2.IMREAD_UNCHANGED)
            if loaded is not None and loaded.shape[2] == 4:
                print("  ✅ PNG透明度支持: 正常")
            else:
                print("  ⚠️  PNG透明度支持: 部分支持")
        else:
            print("  ❌ PNG透明度支持: 失败")
        
        # 清理测试文件
        import os
        if os.path.exists("test_alpha.png"):
            os.remove("test_alpha.png")
            
    except Exception as e:
        print(f"  ❌ PNG透明度支持: {e}")

def check_morphology_operations():
    """检查形态学操作"""
    print("\n测试形态学操作:")
    
    try:
        test_image = np.random.randint(0, 2, (50, 50), dtype=np.uint8) * 255
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        
        # 测试各种形态学操作
        operations = [
            ("开运算", cv2.MORPH_OPEN),
            ("闭运算", cv2.MORPH_CLOSE),
            ("腐蚀", cv2.MORPH_ERODE),
            ("膨胀", cv2.MORPH_DILATE),
        ]
        
        for op_name, op_type in operations:
            try:
                result = cv2.morphologyEx(test_image, op_type, kernel)
                print(f"  ✅ {op_name}: 正常")
            except Exception as e:
                print(f"  ❌ {op_name}: {e}")
                
    except Exception as e:
        print(f"  ❌ 形态学操作测试失败: {e}")

def generate_compatibility_patch():
    """生成兼容性补丁文件"""
    print("\n生成兼容性补丁...")
    
    major, minor, patch = check_opencv_version()
    working_method = test_connected_components()
    
    if working_method is None:
        print("❌ 无法生成补丁，OpenCV版本不兼容")
        return False
    
    method_name, method_func = working_method
    
    # 生成补丁代码
    patch_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV兼容性补丁
自动生成于OpenCV版本: {cv2.__version__}
推荐方法: {method_name}
"""

import cv2
import numpy as np

def safe_connected_components_with_stats(image, connectivity=8):
    """
    兼容不同OpenCV版本的connectedComponentsWithStats函数
    
    Args:
        image: 二值图像
        connectivity: 连通性 (4 或 8)
        
    Returns:
        (num_labels, labels, stats, centroids)
    """
'''
    
    if "ltype" in method_name:
        patch_code += '''
    try:
        return cv2.connectedComponentsWithStats(image, connectivity=connectivity, ltype=cv2.CV_32S)
    except TypeError:
        return cv2.connectedComponentsWithStats(image, connectivity, cv2.CV_32S)
'''
    elif "位置参数" in method_name:
        patch_code += '''
    try:
        return cv2.connectedComponentsWithStats(image, connectivity, cv2.CV_32S)
    except TypeError:
        return cv2.connectedComponentsWithStats(image, connectivity)
'''
    else:
        patch_code += '''
    return cv2.connectedComponentsWithStats(image, connectivity)
'''
    
    patch_code += '''

# 使用示例:
# from opencv_compatibility_patch import safe_connected_components_with_stats
# num_labels, labels, stats, centroids = safe_connected_components_with_stats(binary_image, 8)
'''
    
    # 保存补丁文件
    with open("opencv_compatibility_patch.py", "w", encoding="utf-8") as f:
        f.write(patch_code)
    
    print("✅ 兼容性补丁已生成: opencv_compatibility_patch.py")
    return True

def main():
    """主函数"""
    print("🔧 OpenCV兼容性检查工具")
    print("=" * 50)
    
    # 检查版本
    major, minor, patch = check_opencv_version()
    
    # 测试各项功能
    test_connected_components()
    test_image_io()
    check_morphology_operations()
    
    # 生成兼容性建议
    print("\n💡 兼容性建议:")
    if major >= 4:
        if minor >= 5:
            print("  ✅ OpenCV版本较新，建议使用ltype参数")
        else:
            print("  ⚠️  OpenCV版本中等，建议使用位置参数")
    else:
        print("  ⚠️  OpenCV版本较老，建议升级到4.x版本")
        print("  升级命令: pip install --upgrade opencv-python")
    
    # 生成补丁
    if generate_compatibility_patch():
        print("\n🎯 使用补丁的方法:")
        print("1. 在代码中导入: from opencv_compatibility_patch import safe_connected_components_with_stats")
        print("2. 替换原函数调用")
        print("3. 或者直接修改现有代码使用推荐的参数格式")
    
    print("\n✅ 兼容性检查完成!")

if __name__ == "__main__":
    main()
