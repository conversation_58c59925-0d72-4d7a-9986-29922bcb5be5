#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO功能演示脚本
展示新的文件命名、标记裁剪和YOLO格式生成功能
"""

import os
import sys
import cv2
import numpy as np
import tempfile
import shutil
from PIL import Image

def create_demo_images_with_seeds():
    """创建包含多个种子的演示图像"""
    print("🎨 创建YOLO功能演示图像...")
    
    demo_dir = "yolo_demo"
    if os.path.exists(demo_dir):
        shutil.rmtree(demo_dir)
    os.makedirs(demo_dir)
    
    # 创建不同的演示图像
    demo_configs = [
        {
            'filename': '小麦种子样本.png',
            'description': '包含5个小麦种子',
            'seeds': [
                {'center': (120, 100), 'size': (25, 40), 'color': [200, 180, 120, 255]},
                {'center': (280, 120), 'size': (30, 45), 'color': [210, 190, 130, 255]},
                {'center': (180, 250), 'size': (28, 42), 'color': [190, 170, 110, 255]},
                {'center': (350, 280), 'size': (32, 48), 'color': [205, 185, 125, 255]},
                {'center': (100, 320), 'size': (26, 38), 'color': [195, 175, 115, 255]},
            ]
        },
        {
            'filename': '玉米种子样本.png',
            'description': '包含3个玉米种子',
            'seeds': [
                {'center': (150, 150), 'size': (40, 60), 'color': [220, 200, 80, 255]},
                {'center': (300, 200), 'size': (45, 65), 'color': [230, 210, 90, 255]},
                {'center': (200, 320), 'size': (42, 62), 'color': [225, 205, 85, 255]},
            ]
        },
        {
            'filename': '大豆种子样本.png',
            'description': '包含4个大豆种子',
            'seeds': [
                {'center': (100, 100), 'size': (35, 35), 'color': [180, 160, 100, 255]},
                {'center': (250, 130), 'size': (38, 38), 'color': [185, 165, 105, 255]},
                {'center': (180, 280), 'size': (36, 36), 'color': [175, 155, 95, 255]},
                {'center': (320, 300), 'size': (40, 40), 'color': [190, 170, 110, 255]},
            ]
        }
    ]
    
    created_images = []
    
    for config in demo_configs:
        print(f"  创建: {config['filename']} - {config['description']}")
        
        # 创建透明背景图像 (450x400)
        image = np.zeros((400, 450, 4), dtype=np.uint8)
        
        # 添加种子（椭圆形状）
        for seed in config['seeds']:
            mask = np.zeros((400, 450), dtype=np.uint8)
            cv2.ellipse(mask, seed['center'], seed['size'], 0, 0, 360, 255, -1)
            image[mask > 0] = seed['color']
        
        # 保存图像
        file_path = os.path.join(demo_dir, config['filename'])
        pil_image = Image.fromarray(image, 'RGBA')
        pil_image.save(file_path, 'PNG')
        
        created_images.append({
            'path': file_path,
            'filename': config['filename'],
            'description': config['description'],
            'expected_seeds': len(config['seeds'])
        })
    
    print(f"✅ 演示图像已创建在: {demo_dir}")
    return demo_dir, created_images

def demonstrate_new_features(demo_dir):
    """演示新功能"""
    print(f"\n🚀 演示新功能")
    print("=" * 50)
    
    # 处理演示图像
    output_dir = "yolo_demo_results"
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    
    print(f"1. 处理透明背景图像（新的命名格式）...")
    
    # 使用透明背景分割处理
    try:
        from transparent_seed_segmentation import TransparentSeedSegmentation
        
        config = {
            'alpha_threshold': 50,
            'min_seed_area': 200,
            'max_seed_area': 50000,
            'padding': 15,
            'remove_noise': False,
        }
        
        segmentation = TransparentSeedSegmentation(config)
        
        # 处理每个演示图像
        for image_file in os.listdir(demo_dir):
            if image_file.endswith('.png'):
                image_path = os.path.join(demo_dir, image_file)
                print(f"  处理: {image_file}")
                
                result = segmentation.process_transparent_image(image_path, output_dir)
                
                if result['success']:
                    print(f"    ✅ 成功提取 {result['seeds_count']} 个种子")
                    print(f"    📁 输出目录: {result['output_dir']}")
                    
                    if 'yolo_json_path' in result and result['yolo_json_path']:
                        print(f"    📄 YOLO JSON: {os.path.basename(result['yolo_json_path'])}")
                    
                    if 'marked_crops_dir' in result and result['marked_crops_dir']:
                        print(f"    🖼️  标记裁剪: {os.path.basename(result['marked_crops_dir'])}")
                else:
                    print(f"    ❌ 处理失败: {result.get('error', '未知错误')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

def demonstrate_yolo_dataset_creation(results_dir):
    """演示YOLO数据集创建"""
    print(f"\n📊 创建YOLO训练数据集...")
    
    try:
        from yolo_format_converter import YOLOFormatConverter
        
        converter = YOLOFormatConverter()
        yolo_dataset_dir = "yolo_training_dataset"
        
        if os.path.exists(yolo_dataset_dir):
            shutil.rmtree(yolo_dataset_dir)
        
        # 创建YOLO数据集
        dataset_info = converter.create_yolo_dataset(
            source_dir=results_dir,
            output_dir=yolo_dataset_dir,
            train_ratio=0.7  # 70%训练，30%验证
        )
        
        if dataset_info:
            print(f"✅ YOLO数据集创建成功!")
            print(f"   📁 数据集目录: {dataset_info['dataset_dir']}")
            print(f"   🏋️  训练图像: {dataset_info['train_images']}")
            print(f"   🔍 验证图像: {dataset_info['val_images']}")
            print(f"   📋 配置文件: {os.path.basename(dataset_info['config_file'])}")
            
            # 验证数据集
            print(f"\n🔍 验证YOLO数据集...")
            validation_result = converter.validate_dataset(yolo_dataset_dir)
            
            if validation_result['valid']:
                print("✅ 数据集验证通过")
            else:
                print("⚠️ 数据集验证发现问题")
                for error in validation_result['errors']:
                    print(f"   ❌ {error}")
            
            return dataset_info
        else:
            print("❌ YOLO数据集创建失败")
            return None
            
    except Exception as e:
        print(f"❌ 创建YOLO数据集时出错: {e}")
        return None

def show_file_structure(base_dir):
    """显示文件结构"""
    print(f"\n📁 生成的文件结构:")
    print("-" * 30)
    
    def print_tree(directory, prefix="", max_depth=3, current_depth=0):
        if current_depth >= max_depth:
            return
        
        try:
            items = sorted(os.listdir(directory))
            for i, item in enumerate(items):
                if item.startswith('.'):
                    continue
                
                item_path = os.path.join(directory, item)
                is_last = i == len(items) - 1
                current_prefix = "└── " if is_last else "├── "
                print(f"{prefix}{current_prefix}{item}")
                
                if os.path.isdir(item_path) and current_depth < max_depth - 1:
                    extension = "    " if is_last else "│   "
                    print_tree(item_path, prefix + extension, max_depth, current_depth + 1)
        except PermissionError:
            pass
    
    print_tree(base_dir)

def demonstrate_command_line_usage():
    """演示命令行使用方法"""
    print(f"\n💻 命令行使用示例:")
    print("-" * 30)
    
    print("1. 基本处理（包含新功能）:")
    print("   python transparent_seed_cli.py --input yolo_demo --output results --batch")
    
    print("\n2. 生成YOLO格式并创建数据集:")
    print("   python transparent_seed_cli.py --input yolo_demo --output results --batch --create-yolo-dataset yolo_dataset")
    
    print("\n3. 单独创建YOLO数据集:")
    print("   python yolo_format_converter.py --source results --output yolo_dataset --train-ratio 0.8")
    
    print("\n4. 验证YOLO数据集:")
    print("   python yolo_format_converter.py --source results --output yolo_dataset --validate")

def show_new_features_summary():
    """显示新功能总结"""
    print(f"\n🎯 新功能总结:")
    print("=" * 50)
    
    features = [
        "📝 新的文件命名格式: 原文件名_种子总数_种子ID",
        "🖼️  标记区域裁剪: 保存带边界框的单独图片",
        "📄 YOLO JSON格式: 生成训练用的标注文件",
        "📊 YOLO数据集: 自动创建完整的训练数据集",
        "🔍 数据集验证: 检查数据集完整性和正确性",
        "⚙️  灵活配置: 支持自定义类别和参数"
    ]
    
    for feature in features:
        print(f"  ✅ {feature}")
    
    print(f"\n📋 输出文件说明:")
    print("  • 原文件名_5seeds_001.png - 第1个种子（共5个）")
    print("  • 原文件名_5seeds_002.png - 第2个种子（共5个）")
    print("  • 原文件名_yolo_annotations.json - YOLO标注文件")
    print("  • 原文件名_marked_crops/ - 标记裁剪图片目录")
    print("  • yolo_dataset/ - 完整YOLO训练数据集")

def main():
    """主演示函数"""
    print("🌱 YOLO功能演示")
    print("=" * 60)
    
    try:
        # 创建演示图像
        demo_dir, demo_images = create_demo_images_with_seeds()
        
        # 演示新功能
        if demonstrate_new_features(demo_dir):
            # 创建YOLO数据集
            results_dir = "yolo_demo_results"
            dataset_info = demonstrate_yolo_dataset_creation(results_dir)
            
            # 显示文件结构
            show_file_structure(results_dir)
            
            if dataset_info:
                print(f"\n📁 YOLO数据集结构:")
                show_file_structure(dataset_info['dataset_dir'])
        
        # 显示命令行用法
        demonstrate_command_line_usage()
        
        # 显示新功能总结
        show_new_features_summary()
        
        print(f"\n🎉 演示完成！")
        print(f"现在您可以:")
        print(f"1. 查看生成的文件: yolo_demo_results/")
        print(f"2. 使用YOLO数据集训练模型: yolo_training_dataset/")
        print(f"3. 运行自己的图像处理")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
