#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU-accelerated SAM Everything Installation Test
测试GPU加速SAM Everything的安装和配置
"""

import sys
import os
import traceback

def test_basic_imports():
    """测试基础库导入"""
    print("🔍 测试基础库导入...")
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy导入失败: {e}")
        return False
    
    try:
        from PIL import Image
        print(f"✅ Pillow: 已安装")
    except ImportError as e:
        print(f"❌ Pillow导入失败: {e}")
        return False
    
    return True

def test_pytorch():
    """测试PyTorch和CUDA"""
    print("\n🔍 测试PyTorch和CUDA...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        # 检查CUDA
        cuda_available = torch.cuda.is_available()
        print(f"🔧 CUDA可用: {cuda_available}")
        
        if cuda_available:
            print(f"🎮 GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"   GPU {i}: {gpu_name}")
                
                # 检查显存
                try:
                    memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    print(f"   显存: {memory_total:.1f} GB")
                except:
                    print(f"   显存: 无法获取")
        else:
            print("⚠️  未检测到CUDA GPU，将使用CPU模式")
        
        return True
        
    except ImportError as e:
        print(f"❌ PyTorch导入失败: {e}")
        return False

def test_sam():
    """测试Segment Anything Model"""
    print("\n🔍 测试Segment Anything Model...")
    
    try:
        from segment_anything import sam_model_registry, SamAutomaticMaskGenerator
        print("✅ segment-anything: 已安装")
        
        # 检查模型文件
        model_path = "sam_vit_h_4b8939.pth"
        if os.path.exists(model_path):
            print(f"✅ SAM模型文件: {model_path} 存在")
            
            # 检查文件大小
            file_size = os.path.getsize(model_path) / 1024**3
            print(f"   文件大小: {file_size:.2f} GB")
            
            if file_size < 2.0:
                print("⚠️  模型文件可能不完整，标准大小约2.6GB")
        else:
            print(f"❌ SAM模型文件不存在: {model_path}")
            print("   请下载模型文件:")
            print("   wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ segment-anything导入失败: {e}")
        print("   请安装: pip install segment-anything")
        return False

def test_gpu_sam():
    """测试GPU加速SAM Everything"""
    print("\n🔍 测试GPU加速SAM Everything...")
    
    try:
        from sam_everything_gpu import GPUAcceleratedSAMEverything
        print("✅ GPU加速SAM Everything: 导入成功")
        
        # 测试配置创建
        config = {
            'checkpoint_path': 'sam_vit_h_4b8939.pth',
            'device': 'auto',
            'points_per_side': 32,
            'pred_iou_thresh': 0.88,
            'stability_score_thresh': 0.95,
            'min_object_area': 100,
            'generate_yolo_labels': True,
            'default_class_id': 0,
        }
        
        print("✅ 配置创建: 成功")
        
        # 如果模型文件存在，尝试初始化
        if os.path.exists('sam_vit_h_4b8939.pth'):
            print("🔄 尝试初始化SAM Everything...")
            try:
                sam_everything = GPUAcceleratedSAMEverything(config)
                if sam_everything.sam_generator is not None:
                    print("✅ SAM Everything初始化: 成功")
                else:
                    print("❌ SAM Everything初始化: 失败")
                    return False
            except Exception as e:
                print(f"❌ SAM Everything初始化失败: {e}")
                return False
        else:
            print("⚠️  跳过初始化测试（模型文件不存在）")
        
        return True
        
    except ImportError as e:
        print(f"❌ GPU加速SAM Everything导入失败: {e}")
        return False

def test_cli():
    """测试命令行界面"""
    print("\n🔍 测试命令行界面...")
    
    try:
        import sam_gpu_cli
        print("✅ 命令行界面: 导入成功")
        return True
    except ImportError as e:
        print(f"❌ 命令行界面导入失败: {e}")
        return False

def test_gui():
    """测试GUI界面"""
    print("\n🔍 测试GUI界面...")
    
    try:
        import tkinter as tk
        print("✅ tkinter: 已安装")
    except ImportError as e:
        print(f"❌ tkinter导入失败: {e}")
        print("   在Linux上请安装: sudo apt-get install python3-tk")
        return False
    
    try:
        import sam_everything_gpu_gui
        print("✅ GPU加速GUI: 导入成功")
        return True
    except ImportError as e:
        print(f"❌ GPU加速GUI导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 GPU-accelerated SAM Everything 安装测试")
    print("=" * 50)
    
    tests = [
        ("基础库", test_basic_imports),
        ("PyTorch和CUDA", test_pytorch),
        ("Segment Anything Model", test_sam),
        ("GPU加速SAM Everything", test_gpu_sam),
        ("命令行界面", test_cli),
        ("GUI界面", test_gui),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 恭喜！所有测试都通过了，您可以开始使用GPU加速SAM Everything！")
        print("\n🚀 快速开始:")
        print("   # 命令行模式")
        print("   python sam_gpu_cli.py --device auto --input your_image.jpg --output ./results")
        print("\n   # GUI模式")
        print("   python sam_everything_gpu_gui.py")
    else:
        print("⚠️  部分测试失败，请根据上述错误信息进行修复。")
        print("\n💡 常见解决方案:")
        print("   1. 安装依赖: pip install -r requirements.txt")
        print("   2. 下载模型: wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth")
        print("   3. 检查CUDA: nvidia-smi")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
