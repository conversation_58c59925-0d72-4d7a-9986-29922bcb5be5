#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mask-Based Segmentation for Seed Image Processing
- Process mask images where seeds appear as black regions on white/light background
- Extract individual connected components (seeds) from masks
- Crop corresponding regions from original images
- Preserve scale bars and maintain image quality
"""

import os
import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Optional, Union
from PIL import Image

def safe_connected_components_with_stats(image, connectivity=8):
    """
    兼容不同OpenCV版本的connectedComponentsWithStats函数

    Args:
        image: 二值图像
        connectivity: 连通性 (4 或 8)

    Returns:
        (num_labels, labels, stats, centroids)
    """
    try:
        # 尝试新版本OpenCV (4.5+) 的ltype参数
        return cv2.connectedComponentsWithStats(image, connectivity=connectivity, ltype=cv2.CV_32S)
    except TypeError:
        try:
            # 尝试中等版本的位置参数
            return cv2.connectedComponentsWithStats(image, connectivity, cv2.CV_32S)
        except:
            # 回退到最基本的版本
            return cv2.connectedComponentsWithStats(image, connectivity)
import json


class MaskBasedSegmentation:
    """
    Mask-based segmentation system for processing seed images.
    
    This class processes mask images where seeds appear as black regions
    on a white/light background, extracts individual seeds using connected
    component analysis, and crops corresponding regions from original images.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the mask-based segmentation system.
        
        Args:
            config: Configuration dictionary with processing parameters
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Default configuration values
        self.default_config = {
            'min_seed_area': 100,           # Minimum area for a valid seed (pixels)
            'max_seed_area': 50000,         # Maximum area for a valid seed (pixels)
            'padding': 10,                  # Padding around cropped seeds (pixels)
            'binary_threshold': 127,        # Threshold for binary conversion
            'invert_mask': True,            # Whether to invert mask (black seeds on white)
            'connectivity': 8,              # Connectivity for connected components (4 or 8)
            'preserve_aspect_ratio': True,  # Maintain aspect ratio when cropping
            'output_format': 'png',         # Output format for cropped seeds
            'include_scale_bars': True,     # Whether to detect and preserve scale bars
            'scale_bar_min_aspect': 5.0,    # Minimum aspect ratio for scale bar detection
            'scale_bar_max_area_ratio': 0.1, # Maximum area ratio for scale bars
        }
        
        # Merge user config with defaults
        for key, value in self.default_config.items():
            if key not in self.config:
                self.config[key] = value
        
        self.logger.info("Mask-based segmentation system initialized")
    
    def safe_imread(self, image_path: str) -> Optional[np.ndarray]:
        """
        Safely read an image file, supporting various formats and Chinese paths.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Image array or None if loading failed
        """
        try:
            # Method 1: Direct OpenCV reading
            image = cv2.imread(image_path)
            if image is not None:
                return image
            
            # Method 2: Using numpy and cv2.imdecode for problematic paths
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is not None:
                return image
            
            # Method 3: Using PIL then convert to OpenCV format
            pil_image = Image.open(image_path)
            
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            image_array = np.array(pil_image)
            image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            return image_bgr
            
        except Exception as e:
            self.logger.error(f"Failed to load image {image_path}: {e}")
            return None
    
    def safe_imwrite(self, image_path: str, image: np.ndarray) -> bool:
        """
        Safely write an image file, supporting various formats and Chinese paths.
        
        Args:
            image_path: Path where to save the image
            image: Image array to save
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Method 1: Direct OpenCV writing
            success = cv2.imwrite(image_path, image)
            if success:
                return True
            
            # Method 2: Using cv2.imencode for problematic paths
            ext = os.path.splitext(image_path)[1].lower()
            if ext in ['.jpg', '.jpeg']:
                encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 95]
                success, encoded_img = cv2.imencode('.jpg', image, encode_param)
            elif ext == '.png':
                encode_param = [int(cv2.IMWRITE_PNG_COMPRESSION), 3]
                success, encoded_img = cv2.imencode('.png', image, encode_param)
            else:
                success, encoded_img = cv2.imencode(ext, image)
            
            if success:
                with open(image_path, 'wb') as f:
                    f.write(encoded_img.tobytes())
                return True
            
            # Method 3: Using PIL
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            pil_image.save(image_path)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save image {image_path}: {e}")
            return False

    def validate_mask_image(self, mask_image: np.ndarray) -> bool:
        """
        Validate that the mask image is suitable for processing.

        Args:
            mask_image: Input mask image

        Returns:
            True if valid, False otherwise
        """
        if mask_image is None:
            self.logger.error("Mask image is None")
            return False

        if len(mask_image.shape) not in [2, 3]:
            self.logger.error(f"Invalid mask image shape: {mask_image.shape}")
            return False

        # Check if image has reasonable dimensions
        h, w = mask_image.shape[:2]
        if h < 10 or w < 10:
            self.logger.error(f"Mask image too small: {w}x{h}")
            return False

        return True

    def preprocess_mask(self, mask_image: np.ndarray) -> np.ndarray:
        """
        Preprocess the mask image for connected component analysis.

        Args:
            mask_image: Input mask image

        Returns:
            Binary mask ready for processing
        """
        # Convert to grayscale if needed
        if len(mask_image.shape) == 3:
            gray_mask = cv2.cvtColor(mask_image, cv2.COLOR_BGR2GRAY)
        else:
            gray_mask = mask_image.copy()

        # Apply binary threshold
        threshold = self.config.get('binary_threshold', 127)
        _, binary_mask = cv2.threshold(gray_mask, threshold, 255, cv2.THRESH_BINARY)

        # Invert if needed (black seeds on white background -> white seeds on black)
        if self.config.get('invert_mask', True):
            binary_mask = cv2.bitwise_not(binary_mask)

        # Apply morphological operations to clean up the mask
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel)
        binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)

        return binary_mask

    def find_connected_components(self, binary_mask: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """
        Find connected components in the binary mask.

        Args:
            binary_mask: Binary mask image

        Returns:
            Tuple of (component_info_list, labeled_image)
        """
        connectivity = self.config.get('connectivity', 8)

        # Find connected components (using compatible function)
        num_labels, labels, stats, centroids = safe_connected_components_with_stats(
            binary_mask, connectivity
        )

        components = []
        min_area = self.config.get('min_seed_area', 100)
        max_area = self.config.get('max_seed_area', 50000)

        # Process each component (skip background label 0)
        for i in range(1, num_labels):
            area = stats[i, cv2.CC_STAT_AREA]

            # Filter by area
            if area < min_area or area > max_area:
                continue

            x = stats[i, cv2.CC_STAT_LEFT]
            y = stats[i, cv2.CC_STAT_TOP]
            w = stats[i, cv2.CC_STAT_WIDTH]
            h = stats[i, cv2.CC_STAT_HEIGHT]

            # Calculate additional properties
            aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else float('inf')
            centroid_x, centroid_y = centroids[i]

            component_info = {
                'label': i,
                'area': area,
                'bbox': (x, y, w, h),
                'centroid': (centroid_x, centroid_y),
                'aspect_ratio': aspect_ratio,
            }

            components.append(component_info)

        self.logger.info(f"Found {len(components)} valid seed components")
        return components, labels

    def detect_scale_bars(self, components: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """
        Separate scale bars from seeds based on shape characteristics.

        Args:
            components: List of connected components

        Returns:
            Tuple of (seed_components, scale_bar_components)
        """
        if not self.config.get('include_scale_bars', True):
            return components, []

        seeds = []
        scale_bars = []

        min_aspect = self.config.get('scale_bar_min_aspect', 5.0)
        max_area_ratio = self.config.get('scale_bar_max_area_ratio', 0.1)

        # Calculate total area for ratio comparison
        total_area = sum(comp['area'] for comp in components)

        for comp in components:
            aspect_ratio = comp['aspect_ratio']
            area_ratio = comp['area'] / total_area if total_area > 0 else 0

            # Check if this looks like a scale bar
            is_scale_bar = (
                aspect_ratio >= min_aspect and
                area_ratio <= max_area_ratio
            )

            if is_scale_bar:
                scale_bars.append(comp)
                self.logger.debug(f"Detected scale bar: aspect={aspect_ratio:.2f}, area_ratio={area_ratio:.4f}")
            else:
                seeds.append(comp)

        self.logger.info(f"Separated {len(seeds)} seeds and {len(scale_bars)} scale bars")
        return seeds, scale_bars

    def crop_seed_from_original(self, original_image: np.ndarray, component: Dict,
                               scale_bars: List[Dict] = None) -> Optional[np.ndarray]:
        """
        Crop a seed region from the original image based on component bounding box.

        Args:
            original_image: Original image to crop from
            component: Component information with bounding box
            scale_bars: List of scale bar components to include if they overlap

        Returns:
            Cropped seed image or None if failed
        """
        try:
            x, y, w, h = component['bbox']
            padding = self.config.get('padding', 10)

            # Add padding to bounding box
            x_start = max(0, x - padding)
            y_start = max(0, y - padding)
            x_end = min(original_image.shape[1], x + w + padding)
            y_end = min(original_image.shape[0], y + h + padding)

            # Ensure we have a valid crop region
            if x_end <= x_start or y_end <= y_start:
                self.logger.warning(f"Invalid crop region for component {component['label']}")
                return None

            # Crop the region
            cropped_image = original_image[y_start:y_end, x_start:x_end]

            # Check if any scale bars should be included
            if scale_bars and self.config.get('include_scale_bars', True):
                cropped_image = self._include_scale_bars_in_crop(
                    cropped_image, (x_start, y_start, x_end, y_end),
                    original_image, scale_bars
                )

            return cropped_image

        except Exception as e:
            self.logger.error(f"Failed to crop seed from original image: {e}")
            return None

    def _include_scale_bars_in_crop(self, cropped_image: np.ndarray, crop_region: Tuple[int, int, int, int],
                                   original_image: np.ndarray, scale_bars: List[Dict]) -> np.ndarray:
        """
        Include scale bars that overlap with or are near the crop region.

        Args:
            cropped_image: Already cropped seed image
            crop_region: Crop region coordinates (x_start, y_start, x_end, y_end)
            original_image: Original full image
            scale_bars: List of scale bar components

        Returns:
            Modified cropped image with scale bars included
        """
        x_start, y_start, x_end, y_end = crop_region
        crop_w = x_end - x_start
        crop_h = y_end - y_start

        for scale_bar in scale_bars:
            sb_x, sb_y, sb_w, sb_h = scale_bar['bbox']

            # Check if scale bar overlaps with or is close to crop region
            overlap_threshold = 50  # pixels

            # Calculate distance between crop region and scale bar
            crop_center_x = x_start + crop_w // 2
            crop_center_y = y_start + crop_h // 2
            sb_center_x = sb_x + sb_w // 2
            sb_center_y = sb_y + sb_h // 2

            distance = np.sqrt((crop_center_x - sb_center_x)**2 + (crop_center_y - sb_center_y)**2)

            # If scale bar is close enough, include it
            if distance <= overlap_threshold + max(crop_w, crop_h) // 2:
                # Determine where to place the scale bar in the cropped image
                # For now, we'll overlay it if it fits, or extend the crop region
                self.logger.debug(f"Including scale bar in crop (distance: {distance:.1f})")
                # This is a simplified implementation - could be enhanced
                break

        return cropped_image

    def save_cropped_seed(self, cropped_image: np.ndarray, seed_id: int,
                         output_dir: str, original_filename: str) -> Optional[Dict]:
        """
        Save a cropped seed image to disk.

        Args:
            cropped_image: Cropped seed image
            seed_id: Unique identifier for the seed
            output_dir: Output directory
            original_filename: Original image filename for naming

        Returns:
            Dictionary with saved file information or None if failed
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Generate filename
            base_name = os.path.splitext(original_filename)[0]
            output_format = self.config.get('output_format', 'png')
            seed_filename = f"{base_name}_seed_{seed_id:03d}.{output_format}"
            seed_path = os.path.join(output_dir, seed_filename)

            # Save the image
            success = self.safe_imwrite(seed_path, cropped_image)

            if success:
                h, w = cropped_image.shape[:2]
                file_size = os.path.getsize(seed_path) if os.path.exists(seed_path) else 0

                return {
                    'seed_id': seed_id,
                    'filename': seed_filename,
                    'path': seed_path,
                    'size': (w, h),
                    'file_size': file_size,
                    'format': output_format
                }
            else:
                self.logger.error(f"Failed to save seed image: {seed_path}")
                return None

        except Exception as e:
            self.logger.error(f"Error saving cropped seed: {e}")
            return None

    def process_mask_and_original(self, mask_path: str, original_path: str,
                                 output_dir: str) -> Dict:
        """
        Main processing method that takes a mask image and original image,
        extracts seeds, and saves cropped results.

        Args:
            mask_path: Path to the mask image
            original_path: Path to the original image
            output_dir: Directory to save results

        Returns:
            Processing results dictionary
        """
        start_time = time.time()

        try:
            # Load images
            self.logger.info(f"Loading mask image: {mask_path}")
            mask_image = self.safe_imread(mask_path)
            if mask_image is None:
                return self._create_error_result("Failed to load mask image", start_time)

            self.logger.info(f"Loading original image: {original_path}")
            original_image = self.safe_imread(original_path)
            if original_image is None:
                return self._create_error_result("Failed to load original image", start_time)

            # Validate mask image
            if not self.validate_mask_image(mask_image):
                return self._create_error_result("Invalid mask image", start_time)

            # Check if images have compatible dimensions
            if mask_image.shape[:2] != original_image.shape[:2]:
                self.logger.warning(f"Image size mismatch: mask {mask_image.shape[:2]} vs original {original_image.shape[:2]}")
                # Resize mask to match original if needed
                mask_image = cv2.resize(mask_image, (original_image.shape[1], original_image.shape[0]))

            # Preprocess mask
            self.logger.info("Preprocessing mask image...")
            binary_mask = self.preprocess_mask(mask_image)

            # Find connected components
            self.logger.info("Finding connected components...")
            components, labeled_image = self.find_connected_components(binary_mask)

            if not components:
                return self._create_error_result("No valid seed components found", start_time)

            # Separate seeds from scale bars
            seeds, scale_bars = self.detect_scale_bars(components)

            # Process each seed
            saved_seeds = []
            original_filename = os.path.basename(original_path)

            for i, seed_component in enumerate(seeds, 1):
                self.logger.debug(f"Processing seed {i}/{len(seeds)}")

                # Crop seed from original image
                cropped_seed = self.crop_seed_from_original(
                    original_image, seed_component, scale_bars
                )

                if cropped_seed is not None:
                    # Save cropped seed
                    seed_info = self.save_cropped_seed(
                        cropped_seed, i, output_dir, original_filename
                    )

                    if seed_info:
                        # Add component information
                        seed_info.update({
                            'area': seed_component['area'],
                            'bbox': seed_component['bbox'],
                            'centroid': seed_component['centroid'],
                            'aspect_ratio': seed_component['aspect_ratio']
                        })
                        saved_seeds.append(seed_info)

            # Create visualization
            visualization = self._create_visualization(
                original_image, binary_mask, seeds, scale_bars
            )

            # Save visualization
            vis_filename = f"{os.path.splitext(original_filename)[0]}_mask_segmentation.jpg"
            vis_path = os.path.join(output_dir, vis_filename)
            self.safe_imwrite(vis_path, visualization)

            processing_time = time.time() - start_time

            return {
                'success': True,
                'seeds_count': len(saved_seeds),
                'scale_bars_count': len(scale_bars),
                'processing_time': processing_time,
                'output_dir': output_dir,
                'visualization_path': vis_path,
                'seeds': saved_seeds,
                'method': 'mask_based_segmentation'
            }

        except Exception as e:
            self.logger.error(f"Processing failed: {e}")
            return self._create_error_result(str(e), start_time)

    def _create_error_result(self, error_message: str, start_time: float) -> Dict:
        """Create a standardized error result dictionary."""
        return {
            'success': False,
            'error': error_message,
            'seeds_count': 0,
            'scale_bars_count': 0,
            'processing_time': time.time() - start_time,
            'output_dir': None,
            'seeds': [],
            'method': 'mask_based_segmentation'
        }

    def _create_visualization(self, original_image: np.ndarray, binary_mask: np.ndarray,
                             seeds: List[Dict], scale_bars: List[Dict]) -> np.ndarray:
        """
        Create a visualization showing the segmentation results.

        Args:
            original_image: Original image
            binary_mask: Processed binary mask
            seeds: List of seed components
            scale_bars: List of scale bar components

        Returns:
            Visualization image
        """
        # Create a copy of the original image
        vis_image = original_image.copy()

        # Draw seed bounding boxes in green
        for seed in seeds:
            x, y, w, h = seed['bbox']
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # Add seed ID label
            label = f"S{seed['label']}"
            cv2.putText(vis_image, label, (x, y - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # Draw scale bar bounding boxes in blue
        for scale_bar in scale_bars:
            x, y, w, h = scale_bar['bbox']
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), (255, 0, 0), 2)

            # Add scale bar label
            label = f"SB{scale_bar['label']}"
            cv2.putText(vis_image, label, (x, y - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

        return vis_image

    def get_default_config(self) -> Dict:
        """Get the default configuration for mask-based segmentation."""
        return self.default_config.copy()
