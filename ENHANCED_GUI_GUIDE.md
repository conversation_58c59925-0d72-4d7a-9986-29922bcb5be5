# 增强分割GUI使用指南

## 🎯 概述

增强分割GUI是一个完全中文化的统一分割工具，支持SAM自动分割和掩膜精确分割两种方法。该界面具有智能文件管理、自动配对功能和用户友好的中文界面。

## ✨ 主要特性

### 🌏 完整中文本地化
- **界面元素**: 所有按钮、标签、菜单项均为中文
- **状态消息**: 处理状态和错误信息中文显示
- **参数说明**: 所有参数名称和描述中文化
- **文件对话框**: 中文标题和提示信息

### 📁 增强文件管理
- **自动文件夹扫描**: 选择文件夹后自动列出所有支持的图像文件
- **多格式支持**: .jpg, .jpeg, .png, .bmp, .tiff, .tif
- **文件统计**: 显示文件数量和文件夹路径信息
- **中文文件名支持**: 完美支持中文路径和文件名
- **实时刷新**: 文件列表可随时刷新更新

### 🔗 智能掩膜-图像配对
- **自动配对算法**: 基于文件名智能匹配掩膜和原始图像
- **多种命名约定**: 支持各种常见的文件命名模式
- **配对状态显示**: 可视化显示配对成功和失败的文件
- **手动调整**: 为未配对文件提供手动选择掩膜的功能
- **配对统计**: 实时显示配对成功率和未配对文件警告

## 🚀 快速开始

### 启动程序
```bash
python enhanced_segmentation_gui.py
```

### 基本使用流程
1. **选择分割方法**: SAM自动分割 或 掩膜精确分割
2. **选择文件**: 点击"选择文件夹"或"选择单个文件"
3. **查看配对状态**: (掩膜模式) 检查文件配对结果
4. **调整参数**: 根据需要调整处理参数
5. **开始处理**: 点击"处理当前图像"或"批量处理全部"

## 📋 界面详解

### 分割方法选择
- **SAM (自动分割)**: 使用AI自动检测和分割图像中的所有对象
- **掩膜分割 (精确提取)**: 处理预制掩膜图像，提供像素级精确度

### 文件管理区域
- **选择文件夹**: 自动扫描文件夹中的所有图像文件
- **选择单个文件**: 手动选择一个或多个图像文件
- **文件列表**: 显示所有找到的图像文件
- **刷新文件列表**: 重新扫描当前文件夹

### 文件配对状态 (掩膜模式)
- **配对统计**: 显示成功配对的文件数量
- **未配对警告**: 高亮显示未找到匹配掩膜的文件
- **手动调整配对**: 为未配对文件手动选择掩膜

### 参数设置
#### SAM参数
- **处理设备**: auto/gpu/cpu 选择
- **每边点数**: 控制分割密度 (16-64)
- **最小面积**: 过滤小对象 (50-1000像素)

#### 掩膜参数
- **最小种子面积**: 有效种子的最小面积 (50-1000像素)
- **最大种子面积**: 有效种子的最大面积 (10000-100000像素)
- **裁剪边距**: 种子周围的额外区域 (0-50像素)
- **反转掩膜**: 适用于白底黑种子的掩膜图像

### 操作按钮
- **处理当前图像**: 处理当前选中的图像
- **批量处理全部**: 处理所有文件
- **输出目录**: 设置结果保存位置

### 状态信息
- **当前状态**: 显示处理进度和状态
- **进度条**: 可视化处理进度
- **结果信息**: 显示处理结果统计

## 🔗 自动配对规则

### 支持的命名约定
1. **精确匹配**
   - `种子001.jpg` ↔ `种子001_掩膜.png`
   - `seed001.jpg` ↔ `seed001_mask.png`

2. **后缀匹配**
   - `样本.jpg` ↔ `样本_mask.jpg`
   - `test.png` ↔ `test_m.png`

3. **简化匹配**
   - `image.bmp` ↔ `imagemask.bmp`
   - `data.tiff` ↔ `data掩膜.tiff`

4. **中文支持**
   - `植物种子.jpg` ↔ `植物种子_掩膜.png`
   - `样本图像.png` ↔ `样本图像mask.png`

### 掩膜文件识别关键词
- `mask`, `掩膜`, `_mask`, `_m`, `_掩膜`

## 🎨 掩膜图像要求

### 格式要求
- **种子区域**: 纯黑色 (RGB: 0,0,0)
- **背景区域**: 纯白色 (RGB: 255,255,255)
- **尺寸**: 与原始图像相同尺寸（或自动调整）

### 质量建议
- ✅ 高对比度：纯黑种子，纯白背景
- ✅ 清晰边界：无灰色像素
- ✅ 完整填充：种子区域完全填充
- ✅ 精确形状：掩膜边界与实际种子匹配

## 📊 输出结果

### SAM模式输出
```
output/
├── 图像名称/
│   ├── object_001.png        # 透明背景对象
│   ├── object_002.png
│   ├── 图像名称_result.jpg   # 可视化结果
│   └── yolo_label/
│       └── 图像名称.txt      # YOLO标签
```

### 掩膜模式输出
```
output/
├── 图像名称/
│   ├── 图像名称_seed_001.png           # 单个种子裁剪
│   ├── 图像名称_seed_002.png
│   ├── 图像名称_seed_003.png
│   └── 图像名称_mask_segmentation.jpg  # 可视化结果
```

## 🔧 故障排除

### 常见问题

#### 文件未配对
**症状**: 掩膜模式下显示"✗ 文件名 (无掩膜)"
**解决方案**:
- 检查掩膜文件命名是否包含识别关键词
- 使用"手动调整配对"功能手动选择掩膜
- 确保掩膜文件在同一文件夹中

#### 无种子检测
**症状**: 处理结果显示"提取种子: 0"
**解决方案**:
- 检查掩膜图像是否为白底黑种子格式
- 调整"反转掩膜"设置
- 降低"最小种子面积"参数
- 检查"二值化阈值"设置

#### SAM处理失败
**症状**: "SAM系统不可用"或处理错误
**解决方案**:
- 确保已下载SAM模型文件 `sam_vit_h_4b8939.pth`
- 检查GPU驱动和CUDA安装
- 尝试切换到CPU模式
- 检查内存是否充足

#### 中文路径问题
**症状**: 无法加载中文路径的图像
**解决方案**:
- 系统已内置中文路径支持
- 避免使用特殊字符
- 确保文件路径不过长

### 性能优化

#### 提高处理速度
- 使用GPU模式（SAM）
- 设置合适的面积阈值过滤噪声
- 批量处理时使用较小的图像尺寸

#### 提高结果质量
- 创建高质量的掩膜图像
- 调整参数以适应具体的种子类型
- 使用适当的裁剪边距

## 🧪 测试功能

### 运行测试
```bash
# 运行GUI功能测试
python test_enhanced_gui.py

# 创建演示文件并启动GUI
python demo_enhanced_gui.py
```

### 创建测试文件
```bash
# 创建样本测试图像
python test_mask_based_segmentation.py create-samples
```

## 📚 相关文档

- **[掩膜分割指南](MASK_BASED_SEGMENTATION_GUIDE.md)**: 掩膜分割详细说明
- **[统一README](README_UNIFIED.md)**: 完整系统概述
- **[SAM过滤指南](MASK_FILTERING_SUMMARY.md)**: SAM过滤系统详解

## 🤝 技术支持

如遇到问题，请检查：
1. 所有依赖包是否正确安装
2. 文件路径和命名是否符合要求
3. 系统资源是否充足
4. 参数设置是否合理

---

**版本**: 增强版 v1.0  
**更新日期**: 2024年  
**支持**: 中文界面，智能配对，双重分割方法
