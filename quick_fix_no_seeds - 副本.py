#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复"未找到有效种子组件"问题
自动分析图像并建议最佳参数
"""

import os
import sys
import cv2
import numpy as np
from PIL import Image

def analyze_and_suggest_parameters(image_path):
    """分析图像并建议参数"""
    print(f"🔍 分析图像: {os.path.basename(image_path)}")
    
    if not os.path.exists(image_path):
        print("❌ 文件不存在")
        return None
    
    try:
        # 读取图像
        pil_image = Image.open(image_path)
        
        if pil_image.mode != 'RGBA':
            print(f"⚠️ 图像模式: {pil_image.mode} (不是RGBA)")
            if pil_image.mode == 'RGB':
                print("💡 建议: 图像没有透明度信息，考虑使用SAM或掩膜分割方法")
                return None
            else:
                print("💡 尝试转换为RGBA...")
                pil_image = pil_image.convert('RGBA')
        
        image_array = np.array(pil_image)
        print(f"✅ 图像尺寸: {image_array.shape}")
        
        # 分析alpha通道
        alpha_channel = image_array[:, :, 3]
        unique_alpha = np.unique(alpha_channel)
        
        print(f"Alpha通道唯一值: {unique_alpha}")
        
        # 统计透明度
        transparent_pixels = np.sum(alpha_channel == 0)
        opaque_pixels = np.sum(alpha_channel == 255)
        semi_transparent = alpha_channel.size - transparent_pixels - opaque_pixels
        
        print(f"完全透明: {transparent_pixels} 像素")
        print(f"完全不透明: {opaque_pixels} 像素")
        print(f"半透明: {semi_transparent} 像素")
        
        # 建议alpha阈值
        non_zero_alpha = alpha_channel[alpha_channel > 0]
        if len(non_zero_alpha) > 0:
            suggested_alpha_threshold = max(1, int(non_zero_alpha.min()) - 10)
        else:
            print("❌ 没有非透明像素")
            return None
        
        # 测试不同阈值的连通组件
        best_params = test_different_thresholds(image_array, suggested_alpha_threshold)
        
        return best_params
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def test_different_thresholds(image_array, start_threshold):
    """测试不同阈值找到最佳参数"""
    print(f"\n🧪 测试不同参数组合...")
    
    # 转换为BGRA
    image_bgra = cv2.cvtColor(image_array, cv2.COLOR_RGBA2BGRA)
    
    # 测试参数组合
    test_configs = [
        {'alpha_threshold': start_threshold, 'min_area': 10, 'max_area': 1000000},
        {'alpha_threshold': start_threshold // 2, 'min_area': 10, 'max_area': 1000000},
        {'alpha_threshold': 10, 'min_area': 1, 'max_area': 1000000},
        {'alpha_threshold': 1, 'min_area': 1, 'max_area': 1000000},
    ]
    
    best_config = None
    max_components = 0
    
    for config in test_configs:
        try:
            # 提取alpha掩膜
            alpha_channel = image_bgra[:, :, 3]
            _, alpha_mask = cv2.threshold(alpha_channel, config['alpha_threshold'], 255, cv2.THRESH_BINARY)
            
            # 连通组件分析
            from transparent_seed_segmentation import safe_connected_components_with_stats
            num_labels, labels, stats, centroids = safe_connected_components_with_stats(alpha_mask, 8)
            
            # 计算有效组件
            valid_components = 0
            areas = []
            for i in range(1, num_labels):
                area = stats[i, cv2.CC_STAT_AREA]
                if config['min_area'] <= area <= config['max_area']:
                    valid_components += 1
                    areas.append(area)
            
            print(f"  阈值={config['alpha_threshold']}, 面积范围={config['min_area']}-{config['max_area']}: {valid_components} 个有效组件")
            
            if valid_components > max_components:
                max_components = valid_components
                best_config = config.copy()
                if areas:
                    # 根据实际面积调整范围
                    best_config['suggested_min_area'] = max(1, min(areas) - 10)
                    best_config['suggested_max_area'] = max(areas) + 10000
                    best_config['actual_areas'] = areas
                
        except Exception as e:
            print(f"  测试配置失败: {e}")
    
    return best_config

def generate_command_suggestions(image_path, best_config):
    """生成命令建议"""
    if not best_config:
        print("\n❌ 无法找到有效配置")
        print("💡 建议:")
        print("1. 检查图像是否真的有透明背景")
        print("2. 尝试使用其他分割方法（SAM或掩膜分割）")
        print("3. 手动调整图像，确保种子区域不透明")
        return
    
    print(f"\n✅ 找到最佳配置，可检测到 {len(best_config.get('actual_areas', []))} 个种子")
    
    # 命令行建议
    print(f"\n📋 推荐的命令行参数:")
    cmd = f"python transparent_seed_cli.py --input {image_path} --output results"
    cmd += f" --alpha-threshold {best_config['alpha_threshold']}"
    cmd += f" --min-area {best_config.get('suggested_min_area', best_config['min_area'])}"
    cmd += f" --max-area {best_config.get('suggested_max_area', best_config['max_area'])}"
    cmd += " --no-noise-removal"
    
    print(cmd)
    
    # GUI建议
    print(f"\n🖼️ GUI参数设置:")
    print(f"  透明度阈值: {best_config['alpha_threshold']}")
    print(f"  最小种子面积: {best_config.get('suggested_min_area', best_config['min_area'])}")
    print(f"  最大种子面积: {best_config.get('suggested_max_area', best_config['max_area'])}")
    print(f"  移除噪声: 取消勾选")
    
    # Python API建议
    print(f"\n🐍 Python API配置:")
    print("config = {")
    print(f"    'alpha_threshold': {best_config['alpha_threshold']},")
    print(f"    'min_seed_area': {best_config.get('suggested_min_area', best_config['min_area'])},")
    print(f"    'max_seed_area': {best_config.get('suggested_max_area', best_config['max_area'])},")
    print("    'remove_noise': False,")
    print("    'padding': 10,")
    print("}")
    
    if 'actual_areas' in best_config:
        areas = best_config['actual_areas']
        print(f"\n📊 检测到的种子面积: {areas}")
        print(f"   面积范围: {min(areas)} - {max(areas)} 像素")

def test_recommended_config(image_path, best_config):
    """测试推荐配置"""
    if not best_config:
        return False
    
    print(f"\n🧪 测试推荐配置...")
    
    try:
        from transparent_seed_segmentation import TransparentSeedSegmentation
        
        config = {
            'alpha_threshold': best_config['alpha_threshold'],
            'min_seed_area': best_config.get('suggested_min_area', best_config['min_area']),
            'max_seed_area': best_config.get('suggested_max_area', best_config['max_area']),
            'remove_noise': False,
            'padding': 10,
        }
        
        segmentation = TransparentSeedSegmentation(config)
        
        # 创建临时输出目录
        import tempfile
        temp_dir = tempfile.mkdtemp()
        
        result = segmentation.process_transparent_image(image_path, temp_dir)
        
        if result['success']:
            print(f"✅ 测试成功！检测到 {result['seeds_count']} 个种子")
            print(f"   处理时间: {result['processing_time']:.2f}秒")
            return True
        else:
            print(f"❌ 测试失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python quick_fix_no_seeds.py <图像路径>")
        print("示例: python quick_fix_no_seeds.py seeds.png")
        return 1
    
    image_path = sys.argv[1]
    
    print("🔧 快速修复 '未找到有效种子组件' 问题")
    print("=" * 50)
    
    # 分析图像并建议参数
    best_config = analyze_and_suggest_parameters(image_path)
    
    # 生成建议
    generate_command_suggestions(image_path, best_config)
    
    # 测试建议的配置
    if best_config:
        success = test_recommended_config(image_path, best_config)
        
        if success:
            print(f"\n🎉 问题已解决！使用上述参数可以成功处理您的图像。")
        else:
            print(f"\n⚠️ 推荐配置测试失败，可能需要进一步调整参数。")
    
    print(f"\n💡 更多帮助:")
    print(f"   详细调试: python debug_transparent_segmentation.py {image_path}")
    print(f"   问题指南: 查看 TROUBLESHOOT_NO_SEEDS.md")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
