#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unified Segmentation Command Line Interface
- SAM-based automatic segmentation
- Mask-based segmentation for pre-processed masks
- Device selection (CPU/GPU)
- Batch processing support
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from sam_everything_gpu import GPUAcceleratedSAMEverything
from mask_based_segmentation import MaskBasedSegmentation

def setup_logging(verbose: bool = False):
    """Setup logging configuration"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('segmentation.log', encoding='utf-8')
        ]
    )

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='Unified Segmentation CLI - SAM and Mask-based approaches',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # SAM-based segmentation (automatic)
  python segmentation_cli.py --method sam --device gpu --input image.jpg --output ./results

  # Mask-based segmentation
  python segmentation_cli.py --method mask --mask-image mask.jpg --input original.jpg --output ./results

  # Batch mask-based processing
  python segmentation_cli.py --method mask --mask-dir ./masks --input-dir ./originals --output ./results --batch

  # SAM with custom parameters
  python segmentation_cli.py --method sam --device gpu --input image.jpg --output ./results --points-per-side 64
        """
    )
    
    # Method selection
    parser.add_argument('--method', choices=['sam', 'mask'], required=True,
                       help='Segmentation method: sam (automatic) or mask (pre-processed masks)')
    
    # Basic parameters
    parser.add_argument('--input', '-i', 
                       help='Input image file or directory (for SAM) or original image (for mask)')
    parser.add_argument('--output', '-o', default='./output',
                       help='Output directory (default: ./output)')
    
    # Mask-based specific parameters
    parser.add_argument('--mask-image', 
                       help='Mask image file (for single mask-based processing)')
    parser.add_argument('--mask-dir',
                       help='Directory containing mask images (for batch mask-based processing)')
    parser.add_argument('--input-dir',
                       help='Directory containing original images (for batch mask-based processing)')
    
    # SAM-specific parameters
    parser.add_argument('--device', choices=['cpu', 'gpu', 'cuda', 'auto'], default='auto',
                       help='Device for SAM processing (default: auto)')
    parser.add_argument('--model', default='sam_vit_h_4b8939.pth',
                       help='Path to SAM model file (default: sam_vit_h_4b8939.pth)')
    
    # Processing mode
    parser.add_argument('--batch', action='store_true',
                       help='Batch process multiple images')
    parser.add_argument('--extensions', nargs='+', 
                       default=['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],
                       help='Image file extensions to process')
    
    # SAM parameters
    parser.add_argument('--points-per-side', type=int, default=32,
                       help='SAM: Number of points per side (default: 32)')
    parser.add_argument('--pred-iou-thresh', type=float, default=0.88,
                       help='SAM: Prediction IoU threshold (default: 0.88)')
    parser.add_argument('--stability-score-thresh', type=float, default=0.95,
                       help='SAM: Stability score threshold (default: 0.95)')
    parser.add_argument('--overlap-threshold', type=float, default=0.1,
                       help='SAM: Mask overlap filtering threshold (default: 0.1)')
    parser.add_argument('--max-mask-ratio', type=float, default=0.8,
                       help='SAM: Maximum mask area ratio (default: 0.8)')
    
    # Mask-based parameters
    parser.add_argument('--min-seed-area', type=int, default=100,
                       help='Mask: Minimum seed area in pixels (default: 100)')
    parser.add_argument('--max-seed-area', type=int, default=50000,
                       help='Mask: Maximum seed area in pixels (default: 50000)')
    parser.add_argument('--padding', type=int, default=10,
                       help='Mask: Padding around cropped seeds (default: 10)')
    parser.add_argument('--binary-threshold', type=int, default=127,
                       help='Mask: Binary threshold for mask processing (default: 127)')
    parser.add_argument('--no-invert-mask', action='store_true',
                       help='Mask: Do not invert mask (use if seeds are white on black)')
    
    # Common parameters
    parser.add_argument('--min-area', type=int, default=100,
                       help='Minimum object area in pixels (default: 100)')
    parser.add_argument('--output-format', choices=['png', 'jpg'], default='png',
                       help='Output format for cropped objects (default: png)')
    
    # Other options
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--check-gpu', action='store_true',
                       help='Check GPU availability and exit')
    
    return parser.parse_args()

def check_gpu_availability():
    """Check GPU availability"""
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"CUDA version: {torch.version.cuda}")
            print(f"GPU count: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("No CUDA GPUs available")
            
    except ImportError:
        print("PyTorch not installed")
        return False
    
    return torch.cuda.is_available() if 'torch' in locals() else False

def create_sam_config(args):
    """Create configuration for SAM-based segmentation"""
    return {
        'checkpoint_path': args.model,
        'device': args.device,
        'points_per_side': args.points_per_side,
        'pred_iou_thresh': args.pred_iou_thresh,
        'stability_score_thresh': args.stability_score_thresh,
        'min_object_area': args.min_area,
        'overlap_threshold': args.overlap_threshold,
        'max_mask_ratio': args.max_mask_ratio,
        'use_mask_extraction': True,
        'show_masks': True,
        'mask_alpha': 0.35,
        'generate_yolo_labels': True,
        'default_class_id': 0,
    }

def create_mask_config(args):
    """Create configuration for mask-based segmentation"""
    return {
        'min_seed_area': args.min_seed_area,
        'max_seed_area': args.max_seed_area,
        'padding': args.padding,
        'binary_threshold': args.binary_threshold,
        'invert_mask': not args.no_invert_mask,
        'connectivity': 8,
        'preserve_aspect_ratio': True,
        'output_format': args.output_format,
        'include_scale_bars': True,
        'scale_bar_min_aspect': 5.0,
        'scale_bar_max_area_ratio': 0.1,
    }

def validate_args(args):
    """Validate command line arguments"""
    if args.method == 'sam':
        if not args.input:
            print("Error: --input is required for SAM method")
            return False
        if not os.path.exists(args.model):
            print(f"Error: SAM model file not found: {args.model}")
            return False
    
    elif args.method == 'mask':
        if args.batch:
            if not args.mask_dir or not args.input_dir:
                print("Error: --mask-dir and --input-dir are required for batch mask processing")
                return False
        else:
            if not args.mask_image or not args.input:
                print("Error: --mask-image and --input are required for single mask processing")
                return False
    
    return True

def process_sam_single(sam_everything, input_path, output_dir, logger):
    """Process single image with SAM"""
    logger.info(f"Processing image with SAM: {input_path}")

    # Read image
    image = sam_everything.safe_imread(input_path)
    if image is None:
        logger.error(f"Cannot read image: {input_path}")
        return False

    # Create output directory
    base_name = Path(input_path).stem
    image_output_dir = os.path.join(output_dir, base_name)

    # Process image
    result = sam_everything.process_image(image, input_path, image_output_dir)

    if result['success']:
        logger.info(f"SAM processing successful!")
        logger.info(f"  Objects detected: {result['objects_count']}")
        logger.info(f"  Processing time: {result['processing_time']:.2f}s")
        logger.info(f"  Output directory: {result['output_dir']}")
        return True
    else:
        logger.error(f"SAM processing failed: {result.get('error', 'Unknown error')}")
        return False

def process_mask_single(mask_segmentation, mask_path, original_path, output_dir, logger):
    """Process single image pair with mask-based segmentation"""
    logger.info(f"Processing with mask-based segmentation:")
    logger.info(f"  Mask: {mask_path}")
    logger.info(f"  Original: {original_path}")

    # Create output directory
    base_name = Path(original_path).stem
    image_output_dir = os.path.join(output_dir, base_name)

    # Process images
    result = mask_segmentation.process_mask_and_original(mask_path, original_path, image_output_dir)

    if result['success']:
        logger.info(f"Mask-based processing successful!")
        logger.info(f"  Seeds extracted: {result['seeds_count']}")
        logger.info(f"  Scale bars detected: {result['scale_bars_count']}")
        logger.info(f"  Processing time: {result['processing_time']:.2f}s")
        logger.info(f"  Output directory: {result['output_dir']}")
        return True
    else:
        logger.error(f"Mask-based processing failed: {result.get('error', 'Unknown error')}")
        return False

def process_mask_batch(mask_segmentation, mask_dir, input_dir, output_dir, extensions, logger):
    """Batch process images with mask-based segmentation"""
    logger.info(f"Batch processing with mask-based segmentation:")
    logger.info(f"  Mask directory: {mask_dir}")
    logger.info(f"  Original directory: {input_dir}")

    # Find matching pairs
    mask_files = []
    for ext in extensions:
        mask_files.extend(Path(mask_dir).glob(f"*{ext}"))

    original_files = []
    for ext in extensions:
        original_files.extend(Path(input_dir).glob(f"*{ext}"))

    # Create mapping based on filename stems
    mask_dict = {f.stem: f for f in mask_files}
    original_dict = {f.stem: f for f in original_files}

    # Find matching pairs
    matching_pairs = []
    for stem in mask_dict.keys():
        if stem in original_dict:
            matching_pairs.append((mask_dict[stem], original_dict[stem]))

    logger.info(f"Found {len(matching_pairs)} matching image pairs")

    if not matching_pairs:
        logger.error("No matching image pairs found")
        return False

    # Process each pair
    successful = 0
    total_seeds = 0

    for i, (mask_path, original_path) in enumerate(matching_pairs, 1):
        logger.info(f"Processing pair {i}/{len(matching_pairs)}: {original_path.name}")

        result = mask_segmentation.process_mask_and_original(
            str(mask_path), str(original_path), output_dir
        )

        if result['success']:
            successful += 1
            total_seeds += result['seeds_count']
            logger.info(f"  ✓ Seeds: {result['seeds_count']}, Time: {result['processing_time']:.2f}s")
        else:
            logger.error(f"  ✗ Failed: {result.get('error', 'Unknown error')}")

    logger.info(f"Batch processing completed:")
    logger.info(f"  Successful: {successful}/{len(matching_pairs)}")
    logger.info(f"  Total seeds extracted: {total_seeds}")

    return successful > 0

def main():
    """Main function"""
    args = parse_arguments()

    # Setup logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)

    # Check GPU availability
    if args.check_gpu:
        check_gpu_availability()
        return 0

    # Validate arguments
    if not validate_args(args):
        return 1

    # Create output directory
    os.makedirs(args.output, exist_ok=True)

    start_time = time.time()
    success = False

    if args.method == 'sam':
        # SAM-based processing
        config = create_sam_config(args)
        logger.info("Initializing SAM-based segmentation...")
        sam_everything = GPUAcceleratedSAMEverything(config)

        if sam_everything.sam_generator is None:
            logger.error("SAM initialization failed")
            return 1

        if args.batch or os.path.isdir(args.input):
            # Batch SAM processing
            result = sam_everything.batch_process(args.input, args.output, args.extensions)
            success = result['success']
            if success:
                logger.info(f"SAM batch processing completed!")
                logger.info(f"  Files processed: {result['processed_count']}/{result['total_files']}")
                logger.info(f"  Total objects: {result['total_objects']}")
        else:
            # Single SAM processing
            success = process_sam_single(sam_everything, args.input, args.output, logger)

    elif args.method == 'mask':
        # Mask-based processing
        config = create_mask_config(args)
        logger.info("Initializing mask-based segmentation...")
        mask_segmentation = MaskBasedSegmentation(config)

        if args.batch:
            # Batch mask processing
            success = process_mask_batch(
                mask_segmentation, args.mask_dir, args.input_dir,
                args.output, args.extensions, logger
            )
        else:
            # Single mask processing
            success = process_mask_single(
                mask_segmentation, args.mask_image, args.input,
                args.output, logger
            )

    total_time = time.time() - start_time
    logger.info(f"Total processing time: {total_time:.2f}s")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
