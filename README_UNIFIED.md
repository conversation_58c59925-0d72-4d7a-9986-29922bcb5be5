# SAME - Unified Segmentation System

Advanced segmentation system supporting both **SAM (Segment Anything Model)** automatic detection and **mask-based** precise extraction for seed image processing.

## 🎯 Features

### 🤖 SAM-Based Segmentation (Automatic)
- **GPU Acceleration**: CUDA support for faster processing
- **Automatic Object Detection**: Uses SAM for precise AI-based segmentation
- **YOLO Label Generation**: Automatic YOLO format labels for training datasets
- **Enhanced Mask Filtering**: 
  - Whole-image mask filtering (prevents single large masks)
  - Overlap filtering (removes redundant detections)
  - Size-based filtering
- **Transparent Background Extraction**: PNG output with pixel-perfect transparency

### 🎯 Mask-Based Segmentation (Precise)
- **Pixel-Perfect Accuracy**: Process pre-made mask images for exact seed boundaries
- **Scale Bar Detection**: Automatic detection and separation of scale bars from seeds
- **High-Speed Processing**: Direct mask processing without AI inference
- **Quality Preservation**: Maintains original image quality in cropped seeds
- **Flexible Input**: Supports black seeds on white background masks

### 🖥️ Unified Interface
- **Dual-Method GUI**: Switch between SAM and mask-based approaches
- **Command Line Tools**: Separate CLIs for each method plus unified interface
- **Batch Processing**: Process multiple images efficiently with both methods
- **Adaptive UI**: Optimized for different screen sizes

## 🚀 Quick Start

### Prerequisites

```bash
# For SAM-based segmentation
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install segment-anything

# For both methods
pip install opencv-python pillow numpy
```

### Download SAM Model (for SAM-based segmentation)

```bash
wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
```

### Basic Usage

#### Unified GUI Interface (Recommended)
```bash
python unified_segmentation_gui.py
```

#### Command Line - SAM Method
```bash
# Single image with SAM
python segmentation_cli.py --method sam --device gpu --input image.jpg --output results

# Batch processing with SAM
python segmentation_cli.py --method sam --device gpu --input ./images --batch --output results
```

#### Command Line - Mask Method
```bash
# Single image with mask
python segmentation_cli.py --method mask --mask-image seed_mask.png --input seed_original.jpg --output results

# Batch processing with masks
python segmentation_cli.py --method mask --mask-dir ./masks --input-dir ./originals --output results --batch
```

## 🆚 Method Comparison

| Feature | SAM (Automatic) | Mask-Based (Precise) |
|---------|----------------|----------------------|
| **Input Required** | Original image only | Original + mask image |
| **Accuracy** | AI-based detection | Pixel-perfect (mask dependent) |
| **Speed** | Slower (AI processing) | Faster (direct processing) |
| **Preprocessing** | None required | Mask creation required |
| **Use Case** | General object detection | Precise seed extraction |
| **Scale Bar Detection** | Limited | Built-in detection |
| **GPU Requirement** | Recommended | Not required |

## 📊 Processing Examples

### SAM Processing Results
```
Original masks: 45
├── Whole-image filtering: -2 masks (>80% coverage)
├── Size filtering: -8 masks (<100 pixels)
├── Overlap filtering: -12 masks (>10% overlap)
└── Final objects: 23 masks → 23 PNG files
```

### Mask-Based Processing Results
```
Input: mask + original image
├── Connected components: 5 regions found
├── Scale bar detection: 1 scale bar, 4 seeds
├── Seed extraction: 4 seeds cropped
└── Output: 4 seed PNG files + visualization
```

## 🛠️ Configuration Options

### SAM Parameters
- `points_per_side`: 32 (mask generation density)
- `pred_iou_thresh`: 0.88 (prediction quality)
- `stability_score_thresh`: 0.95 (mask stability)
- `overlap_threshold`: 0.1 (overlap filtering)
- `max_mask_ratio`: 0.8 (whole-image filtering)

### Mask-Based Parameters
- `min_seed_area`: 100 (minimum seed size in pixels)
- `max_seed_area`: 50000 (maximum seed size in pixels)
- `padding`: 10 (padding around cropped seeds)
- `binary_threshold`: 127 (mask binarization threshold)
- `invert_mask`: True (black seeds on white background)

## 📁 Output Structure

### SAM-Based Output
```
output/
├── image1/
│   ├── object_001.png        # Transparent background objects
│   ├── object_002.png
│   ├── image1_result.jpg     # Visualization with masks
│   └── yolo_label/
│       └── image1.txt        # YOLO format labels
```

### Mask-Based Output
```
output/
├── image1/
│   ├── image1_seed_001.png           # Individual seed crops
│   ├── image1_seed_002.png
│   ├── image1_seed_003.png
│   └── image1_mask_segmentation.jpg  # Visualization with bounding boxes
```

## 🎨 Creating Mask Images (for Mask-Based Method)

### Requirements
- **Seeds**: Pure black regions (RGB: 0,0,0)
- **Background**: Pure white (RGB: 255,255,255)
- **Format**: Same dimensions as original image

### Creation Methods
1. **Manual editing** (Photoshop, GIMP) - Most precise
2. **Semi-automatic tools** + manual refinement
3. **Existing segmentation** + binary conversion

## 🧪 Testing

### Run Tests
```bash
# Run all tests
python run_tests.py

# Create sample test images
python test_mask_based_segmentation.py create-samples

# Test mask-based method with samples
python segmentation_cli.py --method mask --mask-image sample_mask.png --input sample_original.jpg --output test_output
```

### Check GPU (for SAM)
```bash
python segmentation_cli.py --check-gpu
```

## 📚 Documentation

- **[Mask-Based Segmentation Guide](MASK_BASED_SEGMENTATION_GUIDE.md)**: Complete mask-based documentation
- **[Mask Filtering Guide](MASK_FILTERING_SUMMARY.md)**: SAM filtering system details
- **[Mask Improvements Guide](MASK_IMPROVEMENTS_GUIDE.md)**: SAM feature overview
- **[Small Screen Guide](SMALL_SCREEN_GUIDE.md)**: GUI optimization details

## 🔧 Troubleshooting

### SAM Issues
- **GPU not detected**: Check CUDA installation, use `--device cpu`
- **Out of memory**: Reduce `points_per_side`, use smaller images
- **Poor results**: Adjust filtering thresholds

### Mask-Based Issues
- **No seeds detected**: Check mask colors, verify `invert_mask` setting
- **Wrong boundaries**: Improve mask accuracy, adjust padding
- **Scale bars as seeds**: Adjust scale bar detection parameters

## 📋 System Requirements

### For SAM Method
- **GPU**: CUDA-compatible (recommended)
- **RAM**: 8GB+ (16GB recommended)
- **VRAM**: 4GB+ (8GB+ recommended)

### For Mask-Based Method
- **CPU**: Any modern processor
- **RAM**: 4GB+ sufficient
- **GPU**: Not required

## 🚀 Performance Tips

### SAM Optimization
- Use GPU when available
- Adjust `points_per_side` for speed/quality balance
- Set appropriate filtering thresholds

### Mask-Based Optimization
- Create high-quality masks
- Use appropriate area thresholds
- Organize files with matching names for batch processing

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new features
4. Submit pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **Meta AI**: For the Segment Anything Model
- **PyTorch Team**: For the deep learning framework
- **OpenCV Community**: For computer vision tools
