# YOLO训练与识别集成指南

## 🎯 概述

透明背景种子分割系统现已完全集成YOLO训练和识别功能，提供从图像分割到模型训练再到智能识别的完整工作流程。

## ✨ 新增功能

### 🤖 YOLO模型管理
- **预设模型**: YOLOv8、YOLOv9、YOLOv10、YOLOv11全系列
- **自动下载**: 一键下载官方预训练模型
- **本地管理**: 智能检测和管理本地模型文件
- **模型信息**: 详细的模型规格和性能参数

### 🏋️ 模型训练
- **数据集集成**: 直接使用分割结果训练
- **参数调节**: 可视化训练参数设置
- **实时监控**: 训练进度和日志实时显示
- **自动保存**: 最佳模型自动保存和管理

### 🔍 智能识别
- **单图预测**: 快速识别单张图像
- **批量处理**: 文件夹批量识别
- **结果可视化**: 带标注的识别结果
- **置信度控制**: 可调节的检测阈值

## 🚀 快速开始

### 1. 安装依赖
```bash
# 自动安装所有YOLO依赖
python install_yolo_dependencies.py

# 或手动安装
pip install ultralytics torch torchvision
```

### 2. 启动GUI
```bash
python enhanced_segmentation_gui.py
```

### 3. 切换到YOLO标签页
在主界面中点击"YOLO训练与识别"标签页

## 📋 完整工作流程

### 步骤1: 图像分割
1. 在"图像分割"标签页选择透明背景分割
2. 启用"生成YOLO标注文件"和"保存标记裁剪图片"
3. 处理您的种子图像
4. 系统自动生成YOLO训练数据

### 步骤2: 创建训练数据集
```bash
# 方法1: 使用GUI快捷按钮
点击"创建YOLO数据集" -> 选择分割结果目录 -> 选择输出目录

# 方法2: 使用命令行
python yolo_format_converter.py --source segmentation_results --output yolo_dataset
```

### 步骤3: 模型管理
1. 切换到"模型管理"子标签页
2. 选择合适的预训练模型（推荐yolov8n用于快速训练）
3. 点击"下载模型"等待下载完成

### 步骤4: 训练模型
1. 切换到"模型训练"子标签页
2. 点击"使用最新分割数据集"自动设置数据集
3. 调整训练参数：
   - **训练轮数**: 50-100（小数据集）
   - **批次大小**: 8-16（根据显存调整）
   - **图像尺寸**: 640（标准尺寸）
4. 点击"开始训练"

### 步骤5: 模型识别
1. 切换到"图像识别"子标签页
2. 选择要识别的图像
3. 调整置信度阈值（推荐0.5）
4. 点击"开始预测"查看结果

## 🎛️ 界面详解

### 模型管理页面
```
┌─ 模型选择器 ─────────────────────────┐
│ [下拉菜单] ✅ yolov8n - YOLOv8 Nano │
│ [刷新] [下载模型]                    │
├─ 模型信息 ─────────────────────────┤
│ 名称: YOLOv8 Nano                   │
│ 描述: 最快速度，适合实时检测         │
│ 大小: 6.2MB | 速度: 极快 | 精度: 中等│
│ 状态: 已下载                        │
└─────────────────────────────────────┘
```

### 训练页面
```
┌─ 训练数据集 ─────────────────────────┐
│ [数据集路径输入框] [选择数据集]       │
│ [使用最新分割数据集] [创建YOLO数据集] │
├─ 训练参数 ─────────────────────────┤
│ 训练轮数: [100] 批次大小: [16]       │
│ 图像尺寸: [640]                     │
├─ 训练控制 ─────────────────────────┤
│ [开始训练] [停止训练]               │
├─ 训练进度 ─────────────────────────┤
│ [进度条]                           │
├─ 训练日志 ─────────────────────────┤
│ [12:34:56] 开始训练...              │
│ [12:34:57] 模型: yolov8n            │
│ [12:34:58] Epoch 1/100...          │
└─────────────────────────────────────┘
```

### 识别页面
```
┌─ 选择图像 ─────────────────────────┐
│ [图像路径输入框] [选择图像]         │
│ [批量预测文件夹] [使用训练好的模型] │
├─ 预测参数 ─────────────────────────┤
│ 置信度阈值: [滑块] 0.5              │
│ ☑ 保存预测结果                     │
├─ 预测控制 ─────────────────────────┤
│ [开始预测]                         │
├─ 预测结果 ─────────────────────────┤
│ 预测完成！                         │
│ 图像: seeds.png                    │
│ 检测到 3 个对象                    │
│                                    │
│ 检测详情:                          │
│ 1. seed                           │
│    置信度: 0.856                   │
│    位置: (45, 67, 125, 147)        │
│    面积: 6400.0 像素               │
└─────────────────────────────────────┘
```

## 🎯 预设模型选择指南

### YOLOv8 系列（推荐）
| 模型 | 大小 | 速度 | 精度 | 适用场景 |
|------|------|------|------|----------|
| yolov8n | 6.2MB | 极快 | 中等 | 实时检测、快速原型 |
| yolov8s | 21.5MB | 快 | 良好 | 平衡性能、生产环境 |
| yolov8m | 49.7MB | 中等 | 高 | 高精度需求 |
| yolov8l | 83.7MB | 慢 | 很高 | 精度优先 |
| yolov8x | 136MB | 很慢 | 最高 | 最高精度要求 |

### YOLOv11 系列（最新）
| 模型 | 大小 | 特点 |
|------|------|------|
| yolov11n | 5.1MB | 最新架构，更高效率 |
| yolov11s | 19.8MB | 改进的特征提取 |
| yolov11m | 48.8MB | 更好的精度平衡 |

### 选择建议
- **初学者**: yolov8n（快速训练和测试）
- **生产环境**: yolov8s（性能平衡）
- **高精度**: yolov8m或yolov11m
- **最新技术**: yolov11系列

## ⚙️ 训练参数优化

### 基础参数
- **训练轮数 (epochs)**:
  - 小数据集: 50-100
  - 中等数据集: 100-200
  - 大数据集: 200-300

- **批次大小 (batch_size)**:
  - 4GB显存: 4-8
  - 8GB显存: 8-16
  - 16GB显存: 16-32

- **图像尺寸 (img_size)**:
  - 标准: 640
  - 高精度: 1280
  - 快速: 320

### 高级优化
```python
# 在训练配置中可以添加
learning_rate: 0.01      # 学习率
weight_decay: 0.0005     # 权重衰减
momentum: 0.937          # 动量
warmup_epochs: 3         # 预热轮数
```

## 📊 结果分析

### 训练指标
- **mAP50**: 在IoU=0.5时的平均精度
- **mAP50-95**: 在IoU=0.5-0.95时的平均精度
- **Precision**: 精确率
- **Recall**: 召回率

### 预测结果
- **置信度**: 模型对检测的确信程度
- **边界框**: 检测对象的位置坐标
- **类别**: 检测到的对象类别
- **面积**: 检测框的像素面积

## 🔧 故障排除

### 常见问题

#### 1. YOLO功能不可用
**症状**: GUI中没有YOLO标签页
**解决**: 
```bash
python install_yolo_dependencies.py
```

#### 2. 模型下载失败
**症状**: 下载进度卡住或报错
**解决**: 
- 检查网络连接
- 尝试使用VPN
- 手动下载模型文件

#### 3. 训练内存不足
**症状**: CUDA out of memory
**解决**: 
- 减小批次大小
- 降低图像尺寸
- 使用更小的模型

#### 4. 训练精度低
**症状**: mAP值很低
**解决**: 
- 增加训练数据
- 调整学习率
- 增加训练轮数
- 检查标注质量

### 调试命令
```bash
# 检查YOLO安装
python -c "from ultralytics import YOLO; print('YOLO OK')"

# 测试模型下载
python -c "from ultralytics import YOLO; YOLO('yolov8n.pt')"

# 验证数据集
python yolo_format_converter.py --source results --output dataset --validate

# 运行完整演示
python demo_yolo_integration.py
```

## 📚 相关文档

- **[YOLO功能指南](YOLO_FEATURES_GUIDE.md)**: 详细功能说明
- **[透明背景分割指南](TRANSPARENT_SEGMENTATION_GUIDE.md)**: 基础分割功能
- **[增强GUI指南](ENHANCED_GUI_GUIDE.md)**: 界面使用说明

## 🔗 外部资源

- **[Ultralytics官方文档](https://docs.ultralytics.com/)**
- **[YOLOv8教程](https://github.com/ultralytics/ultralytics)**
- **[YOLO训练最佳实践](https://docs.ultralytics.com/guides/)**

---

**版本**: v3.0  
**更新日期**: 2024年  
**新功能**: 完整YOLO训练与识别集成
