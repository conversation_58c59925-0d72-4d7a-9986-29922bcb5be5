#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Segmentation GUI Demo
演示增强分割GUI的功能和特性
"""

import os
import sys
import cv2
import numpy as np
import tempfile
import shutil

def create_demo_files():
    """创建演示文件"""
    print("创建演示文件...")
    
    # 创建演示目录
    demo_dir = "demo_files"
    if os.path.exists(demo_dir):
        shutil.rmtree(demo_dir)
    os.makedirs(demo_dir)
    
    # 创建不同命名约定的文件对
    file_pairs = [
        ("种子样本001.jpg", "种子样本001_掩膜.png"),
        ("seed_sample_A.jpg", "seed_sample_A_mask.png"),
        ("植物种子_002.png", "植物种子_002.png"),  # 掩膜文件同名
        ("test_image.bmp", "test_image_m.bmp"),
        ("样本图像.tiff", "样本图像_mask.tiff"),
    ]
    
    for i, (img_name, mask_name) in enumerate(file_pairs):
        # 创建原始图像
        img_path = os.path.join(demo_dir, img_name)
        original = create_demo_original(600, 400, i)
        cv2.imwrite(img_path, original)
        
        # 创建掩膜图像
        mask_path = os.path.join(demo_dir, mask_name)
        mask = create_demo_mask(600, 400, i)
        cv2.imwrite(mask_path, mask)
        
        print(f"  创建文件对: {img_name} + {mask_name}")
    
    # 创建一个没有配对的图像
    orphan_path = os.path.join(demo_dir, "无配对图像.jpg")
    cv2.imwrite(orphan_path, create_demo_original(300, 300, 99))
    print(f"  创建未配对文件: 无配对图像.jpg")
    
    print(f"演示文件已创建在: {demo_dir}")
    return demo_dir

def create_demo_original(width, height, seed):
    """创建演示原始图像"""
    np.random.seed(seed)
    
    # 创建渐变背景
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 水平渐变
    for x in range(width):
        color_value = int(255 * x / width)
        image[:, x, 0] = color_value  # 红色通道
    
    # 垂直渐变
    for y in range(height):
        color_value = int(255 * y / height)
        image[y, :, 1] = color_value  # 绿色通道
    
    # 固定蓝色
    image[:, :, 2] = 100
    
    # 添加种子形状
    center_x, center_y = width // 2, height // 2
    
    # 大椭圆种子
    cv2.ellipse(image, (center_x - 100, center_y - 80), (60, 80), 0, 0, 360, (255, 255, 255), -1)
    cv2.ellipse(image, (center_x - 100, center_y - 80), (60, 80), 0, 0, 360, (0, 0, 0), 2)
    
    # 中等圆形种子
    cv2.circle(image, (center_x + 80, center_y - 40), 45, (255, 255, 255), -1)
    cv2.circle(image, (center_x + 80, center_y - 40), 45, (0, 0, 0), 2)
    
    # 小椭圆种子
    cv2.ellipse(image, (center_x - 20, center_y + 80), (35, 50), 45, 0, 360, (255, 255, 255), -1)
    cv2.ellipse(image, (center_x - 20, center_y + 80), (35, 50), 45, 0, 360, (0, 0, 0), 2)
    
    # 比例尺
    scale_bar_y = height - 50
    cv2.rectangle(image, (50, scale_bar_y), (150, scale_bar_y + 10), (0, 0, 0), -1)
    cv2.putText(image, "1cm", (50, scale_bar_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    # 添加一些纹理
    noise = np.random.randint(0, 30, (height, width, 3), dtype=np.uint8)
    image = cv2.add(image, noise)
    
    return image

def create_demo_mask(width, height, seed):
    """创建演示掩膜图像（白底黑种子）"""
    # 白色背景
    mask = np.ones((height, width), dtype=np.uint8) * 255
    
    # 添加黑色种子区域
    center_x, center_y = width // 2, height // 2
    
    # 大椭圆种子
    cv2.ellipse(mask, (center_x - 100, center_y - 80), (60, 80), 0, 0, 360, 0, -1)
    
    # 中等圆形种子
    cv2.circle(mask, (center_x + 80, center_y - 40), 45, 0, -1)
    
    # 小椭圆种子
    cv2.ellipse(mask, (center_x - 20, center_y + 80), (35, 50), 45, 0, 360, 0, -1)
    
    # 比例尺（作为独立的黑色区域）
    scale_bar_y = height - 50
    cv2.rectangle(mask, (50, scale_bar_y), (150, scale_bar_y + 10), 0, -1)
    
    return mask

def print_usage_guide():
    """打印使用指南"""
    print("\n" + "=" * 60)
    print("增强分割GUI使用指南")
    print("=" * 60)
    
    print("\n🎯 主要功能:")
    print("1. 中文界面 - 所有界面元素都使用中文")
    print("2. 智能文件管理 - 自动扫描文件夹中的图像文件")
    print("3. 自动配对 - 智能匹配掩膜文件和原始图像")
    print("4. 双重分割方法 - 支持SAM自动分割和掩膜精确分割")
    
    print("\n📁 文件管理特性:")
    print("• 支持多种图像格式: .jpg, .jpeg, .png, .bmp, .tiff")
    print("• 自动识别掩膜文件（包含'mask'、'掩膜'、'_m'等关键词）")
    print("• 显示文件统计和配对状态")
    print("• 支持中文文件名")
    
    print("\n🔗 自动配对规则:")
    print("• 精确匹配: seed001.jpg ↔ seed001_mask.png")
    print("• 后缀匹配: sample.jpg ↔ sample_mask.jpg")
    print("• 简化匹配: test.png ↔ testmask.png")
    print("• 中文匹配: 种子.jpg ↔ 种子_掩膜.png")
    
    print("\n🎮 使用步骤:")
    print("1. 启动GUI: python enhanced_segmentation_gui.py")
    print("2. 选择分割方法（SAM或掩膜分割）")
    print("3. 点击'选择文件夹'选择包含图像的目录")
    print("4. 查看自动配对结果（掩膜模式）")
    print("5. 如需要，使用'手动调整配对'功能")
    print("6. 调整参数设置")
    print("7. 点击'处理当前图像'或'批量处理全部'")
    
    print("\n⚙️ 参数说明:")
    print("SAM模式:")
    print("  • 处理设备: auto/gpu/cpu")
    print("  • 每边点数: 控制分割密度")
    print("  • 最小面积: 过滤小对象")
    
    print("掩膜模式:")
    print("  • 最小/最大种子面积: 种子大小范围")
    print("  • 裁剪边距: 种子周围的额外区域")
    print("  • 反转掩膜: 适用于白底黑种子的掩膜")
    
    print("\n🔧 故障排除:")
    print("• 如果文件未配对，检查文件命名是否符合规则")
    print("• 使用'手动调整配对'为未配对文件选择掩膜")
    print("• 确保掩膜图像是白底黑种子格式")
    print("• SAM模式需要下载模型文件 sam_vit_h_4b8939.pth")

def main():
    """主函数"""
    print("增强分割GUI演示程序")
    print("=" * 40)
    
    # 创建演示文件
    demo_dir = create_demo_files()
    
    # 打印使用指南
    print_usage_guide()
    
    print(f"\n📂 演示文件位置: {os.path.abspath(demo_dir)}")
    print("\n🚀 启动GUI:")
    print("python enhanced_segmentation_gui.py")
    
    print("\n💡 提示:")
    print("1. 启动GUI后，点击'选择文件夹'并选择demo_files目录")
    print("2. 切换到'掩膜分割'模式查看自动配对结果")
    print("3. 选择任意文件查看图像预览")
    print("4. 调整参数后点击'处理当前图像'进行测试")
    
    # 询问是否启动GUI
    try:
        response = input("\n是否现在启动GUI? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是', '启动']:
            print("启动增强分割GUI...")
            try:
                from enhanced_segmentation_gui import main as gui_main
                gui_main()
            except ImportError:
                print("❌ 无法导入GUI模块，请确保enhanced_segmentation_gui.py文件存在")
            except Exception as e:
                print(f"❌ 启动GUI时出错: {e}")
        else:
            print("演示完成。您可以稍后手动启动GUI。")
    except KeyboardInterrupt:
        print("\n演示结束。")

if __name__ == "__main__":
    main()
