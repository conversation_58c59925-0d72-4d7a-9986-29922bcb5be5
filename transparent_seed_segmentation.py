#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
透明背景种子分割系统
专门处理PNG透明背景图像，自动检测和分离种子对象
- 基于alpha通道的种子检测
- 连通组件分析分离独立种子
- 保持透明背景的种子裁剪
- 批量处理支持
"""

import os
import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Optional, Union
from PIL import Image
import json
from pathlib import Path


class TransparentSeedSegmentation:
    """
    透明背景种子分割系统
    
    专门处理PNG格式的透明背景图像，通过分析alpha通道检测种子对象，
    并将每个独立的种子分离为单独的图像文件。
    """
    
    def __init__(self, config: Dict):
        """
        初始化透明背景种子分割系统
        
        Args:
            config: 配置字典，包含处理参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 默认配置值
        self.default_config = {
            'min_seed_area': 100,           # 最小种子面积（像素）
            'max_seed_area': 100000,        # 最大种子面积（像素）
            'padding': 10,                  # 裁剪边距（像素）
            'alpha_threshold': 128,         # alpha通道阈值（0-255）
            'connectivity': 8,              # 连通性（4或8）
            'morphology_kernel_size': 3,    # 形态学操作核大小
            'min_alpha_ratio': 0.1,         # 最小非透明像素比例
            'output_format': 'png',         # 输出格式
            'preserve_quality': True,       # 保持原始质量
            'remove_noise': True,           # 移除噪声
        }
        
        # 合并用户配置和默认配置
        for key, value in self.default_config.items():
            if key not in self.config:
                self.config[key] = value
        
        self.logger.info("透明背景种子分割系统初始化完成")
    
    def safe_imread_with_alpha(self, image_path: str) -> Optional[np.ndarray]:
        """
        安全读取带alpha通道的PNG图像
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            BGRA格式的图像数组，如果失败返回None
        """
        try:
            # 方法1: 使用PIL读取，确保保留alpha通道
            pil_image = Image.open(image_path)
            
            # 确保图像有alpha通道
            if pil_image.mode != 'RGBA':
                if pil_image.mode == 'RGB':
                    # RGB图像添加完全不透明的alpha通道
                    pil_image = pil_image.convert('RGBA')
                elif pil_image.mode in ['L', 'P']:
                    # 灰度或调色板图像转换为RGBA
                    pil_image = pil_image.convert('RGBA')
                else:
                    self.logger.warning(f"不支持的图像模式: {pil_image.mode}")
                    return None
            
            # 转换为numpy数组（RGBA -> BGRA for OpenCV）
            image_array = np.array(pil_image)
            image_bgra = cv2.cvtColor(image_array, cv2.COLOR_RGBA2BGRA)
            
            return image_bgra
            
        except Exception as e:
            self.logger.error(f"读取图像失败 {image_path}: {e}")
            
            # 方法2: 尝试使用OpenCV读取
            try:
                image = cv2.imread(image_path, cv2.IMREAD_UNCHANGED)
                if image is not None and len(image.shape) == 3 and image.shape[2] == 4:
                    return image
                elif image is not None and len(image.shape) == 3 and image.shape[2] == 3:
                    # 添加alpha通道
                    h, w = image.shape[:2]
                    alpha = np.ones((h, w, 1), dtype=image.dtype) * 255
                    image_with_alpha = np.concatenate([image, alpha], axis=2)
                    return image_with_alpha
            except Exception as e2:
                self.logger.error(f"OpenCV读取也失败: {e2}")
            
            return None
    
    def safe_imwrite_with_alpha(self, image_path: str, image: np.ndarray) -> bool:
        """
        安全保存带alpha通道的PNG图像
        
        Args:
            image_path: 保存路径
            image: BGRA格式的图像数组
            
        Returns:
            保存是否成功
        """
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(image_path), exist_ok=True)
            
            # 方法1: 使用PIL保存，确保保留alpha通道
            if len(image.shape) == 3 and image.shape[2] == 4:
                # BGRA -> RGBA for PIL
                image_rgba = cv2.cvtColor(image, cv2.COLOR_BGRA2RGBA)
                pil_image = Image.fromarray(image_rgba, 'RGBA')
                
                # 保存为PNG格式，保留透明度
                pil_image.save(image_path, 'PNG', optimize=False, compress_level=1)
                return True
            else:
                self.logger.error(f"图像格式不正确，需要4通道BGRA格式")
                return False
                
        except Exception as e:
            self.logger.error(f"保存图像失败 {image_path}: {e}")
            
            # 方法2: 尝试使用OpenCV保存
            try:
                success = cv2.imwrite(image_path, image)
                return success
            except Exception as e2:
                self.logger.error(f"OpenCV保存也失败: {e2}")
                return False
    
    def extract_alpha_mask(self, image_bgra: np.ndarray) -> np.ndarray:
        """
        从BGRA图像中提取alpha通道掩膜
        
        Args:
            image_bgra: BGRA格式的图像
            
        Returns:
            二值化的alpha掩膜
        """
        # 提取alpha通道
        alpha_channel = image_bgra[:, :, 3]
        
        # 应用阈值
        alpha_threshold = self.config.get('alpha_threshold', 128)
        _, alpha_mask = cv2.threshold(alpha_channel, alpha_threshold, 255, cv2.THRESH_BINARY)
        
        # 形态学操作去除噪声
        if self.config.get('remove_noise', True):
            kernel_size = self.config.get('morphology_kernel_size', 3)
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
            
            # 开运算去除小噪声
            alpha_mask = cv2.morphologyEx(alpha_mask, cv2.MORPH_OPEN, kernel)
            # 闭运算填充小孔洞
            alpha_mask = cv2.morphologyEx(alpha_mask, cv2.MORPH_CLOSE, kernel)
        
        return alpha_mask
    
    def find_seed_components(self, alpha_mask: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """
        在alpha掩膜中查找种子连通组件
        
        Args:
            alpha_mask: 二值化的alpha掩膜
            
        Returns:
            (组件信息列表, 标记图像)
        """
        connectivity = self.config.get('connectivity', 8)
        
        # 查找连通组件
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(
            alpha_mask, connectivity=connectivity, dtype=cv2.CV_32S
        )
        
        components = []
        min_area = self.config.get('min_seed_area', 100)
        max_area = self.config.get('max_seed_area', 100000)
        min_alpha_ratio = self.config.get('min_alpha_ratio', 0.1)
        
        # 处理每个组件（跳过背景标签0）
        for i in range(1, num_labels):
            area = stats[i, cv2.CC_STAT_AREA]
            
            # 面积过滤
            if area < min_area or area > max_area:
                continue
            
            x = stats[i, cv2.CC_STAT_LEFT]
            y = stats[i, cv2.CC_STAT_TOP]
            w = stats[i, cv2.CC_STAT_WIDTH]
            h = stats[i, cv2.CC_STAT_HEIGHT]
            
            # 计算非透明像素比例
            component_mask = (labels == i).astype(np.uint8) * 255
            bbox_area = w * h
            alpha_ratio = area / bbox_area if bbox_area > 0 else 0
            
            # 过滤掉非透明像素比例太低的组件
            if alpha_ratio < min_alpha_ratio:
                continue
            
            # 计算其他属性
            aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else float('inf')
            centroid_x, centroid_y = centroids[i]
            
            component_info = {
                'label': i,
                'area': area,
                'bbox': (x, y, w, h),
                'centroid': (centroid_x, centroid_y),
                'aspect_ratio': aspect_ratio,
                'alpha_ratio': alpha_ratio,
                'component_mask': component_mask,
            }
            
            components.append(component_info)
        
        self.logger.info(f"找到 {len(components)} 个有效种子组件")
        return components, labels
    
    def crop_seed_with_alpha(self, image_bgra: np.ndarray, component: Dict) -> Optional[np.ndarray]:
        """
        裁剪带透明背景的种子对象
        
        Args:
            image_bgra: 原始BGRA图像
            component: 组件信息
            
        Returns:
            裁剪后的种子图像（BGRA格式）
        """
        try:
            x, y, w, h = component['bbox']
            padding = self.config.get('padding', 10)
            
            # 添加边距到边界框
            x_start = max(0, x - padding)
            y_start = max(0, y - padding)
            x_end = min(image_bgra.shape[1], x + w + padding)
            y_end = min(image_bgra.shape[0], y + h + padding)
            
            # 确保有效的裁剪区域
            if x_end <= x_start or y_end <= y_start:
                self.logger.warning(f"无效的裁剪区域，组件 {component['label']}")
                return None
            
            # 裁剪图像
            cropped_image = image_bgra[y_start:y_end, x_start:x_end].copy()
            
            # 创建精确的alpha掩膜
            component_mask = component['component_mask']
            cropped_mask = component_mask[y_start:y_end, x_start:x_end]
            
            # 应用掩膜到alpha通道，确保只有种子部分不透明
            if cropped_mask.shape[:2] == cropped_image.shape[:2]:
                # 创建新的alpha通道
                new_alpha = np.zeros(cropped_image.shape[:2], dtype=np.uint8)
                
                # 在种子区域保持原始alpha值
                seed_pixels = cropped_mask > 0
                new_alpha[seed_pixels] = cropped_image[seed_pixels, 3]
                
                # 更新alpha通道
                cropped_image[:, :, 3] = new_alpha
            
            return cropped_image
            
        except Exception as e:
            self.logger.error(f"裁剪种子时出错: {e}")
            return None
    
    def save_cropped_seed(self, cropped_seed: np.ndarray, seed_id: int, 
                         output_dir: str, original_filename: str) -> Optional[Dict]:
        """
        保存裁剪的种子图像
        
        Args:
            cropped_seed: 裁剪的种子图像（BGRA格式）
            seed_id: 种子ID
            output_dir: 输出目录
            original_filename: 原始文件名
            
        Returns:
            保存的文件信息字典
        """
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            base_name = os.path.splitext(original_filename)[0]
            seed_filename = f"{base_name}_seed_{seed_id:03d}.png"
            seed_path = os.path.join(output_dir, seed_filename)
            
            # 保存图像
            success = self.safe_imwrite_with_alpha(seed_path, cropped_seed)
            
            if success:
                h, w = cropped_seed.shape[:2]
                file_size = os.path.getsize(seed_path) if os.path.exists(seed_path) else 0
                
                return {
                    'seed_id': seed_id,
                    'filename': seed_filename,
                    'path': seed_path,
                    'size': (w, h),
                    'file_size': file_size,
                    'format': 'png',
                    'has_transparency': True
                }
            else:
                self.logger.error(f"保存种子图像失败: {seed_path}")
                return None
                
        except Exception as e:
            self.logger.error(f"保存裁剪种子时出错: {e}")
            return None

    def process_transparent_image(self, image_path: str, output_dir: str) -> Dict:
        """
        处理单张透明背景图像

        Args:
            image_path: 输入图像路径
            output_dir: 输出目录

        Returns:
            处理结果字典
        """
        start_time = time.time()

        try:
            # 读取带alpha通道的图像
            self.logger.info(f"读取透明背景图像: {image_path}")
            image_bgra = self.safe_imread_with_alpha(image_path)
            if image_bgra is None:
                return self._create_error_result("无法读取图像文件", start_time)

            # 验证图像格式
            if len(image_bgra.shape) != 3 or image_bgra.shape[2] != 4:
                return self._create_error_result("图像格式不正确，需要BGRA格式", start_time)

            # 提取alpha掩膜
            self.logger.info("分析alpha通道...")
            alpha_mask = self.extract_alpha_mask(image_bgra)

            # 检查是否有非透明区域
            non_transparent_pixels = np.sum(alpha_mask > 0)
            if non_transparent_pixels == 0:
                return self._create_error_result("图像中没有非透明区域", start_time)

            # 查找种子组件
            self.logger.info("查找种子组件...")
            components, labeled_image = self.find_seed_components(alpha_mask)

            if not components:
                return self._create_error_result("未找到有效的种子组件", start_time)

            # 处理每个种子
            saved_seeds = []
            original_filename = os.path.basename(image_path)

            for i, component in enumerate(components, 1):
                self.logger.debug(f"处理种子 {i}/{len(components)}")

                # 裁剪种子
                cropped_seed = self.crop_seed_with_alpha(image_bgra, component)

                if cropped_seed is not None:
                    # 保存裁剪的种子
                    seed_info = self.save_cropped_seed(
                        cropped_seed, i, output_dir, original_filename
                    )

                    if seed_info:
                        # 添加组件信息
                        seed_info.update({
                            'area': component['area'],
                            'bbox': component['bbox'],
                            'centroid': component['centroid'],
                            'aspect_ratio': component['aspect_ratio'],
                            'alpha_ratio': component['alpha_ratio']
                        })
                        saved_seeds.append(seed_info)

            # 创建可视化图像
            visualization = self._create_visualization(
                image_bgra, alpha_mask, components
            )

            # 保存可视化
            vis_filename = f"{os.path.splitext(original_filename)[0]}_transparent_segmentation.jpg"
            vis_path = os.path.join(output_dir, vis_filename)
            if visualization is not None:
                # 转换为BGR格式保存
                vis_bgr = cv2.cvtColor(visualization, cv2.COLOR_BGRA2BGR)
                cv2.imwrite(vis_path, vis_bgr)

            processing_time = time.time() - start_time

            return {
                'success': True,
                'seeds_count': len(saved_seeds),
                'processing_time': processing_time,
                'output_dir': output_dir,
                'visualization_path': vis_path if visualization is not None else None,
                'seeds': saved_seeds,
                'method': 'transparent_seed_segmentation',
                'non_transparent_pixels': int(non_transparent_pixels),
                'total_components_found': len(components)
            }

        except Exception as e:
            self.logger.error(f"处理失败: {e}")
            return self._create_error_result(str(e), start_time)

    def batch_process_transparent_images(self, input_dir: str, output_dir: str,
                                       extensions: List[str] = None) -> Dict:
        """
        批量处理透明背景图像

        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            extensions: 支持的文件扩展名列表

        Returns:
            批量处理结果字典
        """
        if extensions is None:
            extensions = ['.png']  # 主要处理PNG格式

        start_time = time.time()

        try:
            # 扫描输入目录
            image_files = []
            for ext in extensions:
                pattern = os.path.join(input_dir, f"*{ext}")
                import glob
                files = glob.glob(pattern, recursive=False)
                image_files.extend(files)

            if not image_files:
                return {
                    'success': False,
                    'error': f"在目录 {input_dir} 中未找到支持的图像文件",
                    'processed_count': 0,
                    'total_files': 0,
                    'total_seeds': 0,
                    'processing_time': time.time() - start_time
                }

            self.logger.info(f"找到 {len(image_files)} 个图像文件进行批量处理")

            # 处理每个文件
            processed_count = 0
            total_seeds = 0
            failed_files = []

            for i, image_path in enumerate(image_files, 1):
                self.logger.info(f"处理文件 {i}/{len(image_files)}: {os.path.basename(image_path)}")

                # 为每个图像创建子目录
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                image_output_dir = os.path.join(output_dir, base_name)

                # 处理图像
                result = self.process_transparent_image(image_path, image_output_dir)

                if result['success']:
                    processed_count += 1
                    total_seeds += result['seeds_count']
                    self.logger.info(f"  ✓ 成功提取 {result['seeds_count']} 个种子")
                else:
                    failed_files.append({
                        'file': os.path.basename(image_path),
                        'error': result.get('error', '未知错误')
                    })
                    self.logger.error(f"  ✗ 处理失败: {result.get('error', '未知错误')}")

            processing_time = time.time() - start_time

            return {
                'success': True,
                'processed_count': processed_count,
                'total_files': len(image_files),
                'total_seeds': total_seeds,
                'failed_files': failed_files,
                'processing_time': processing_time,
                'output_dir': output_dir,
                'method': 'transparent_seed_segmentation_batch'
            }

        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processed_count': 0,
                'total_files': 0,
                'total_seeds': 0,
                'processing_time': time.time() - start_time
            }

    def _create_error_result(self, error_message: str, start_time: float) -> Dict:
        """创建标准化的错误结果字典"""
        return {
            'success': False,
            'error': error_message,
            'seeds_count': 0,
            'processing_time': time.time() - start_time,
            'output_dir': None,
            'seeds': [],
            'method': 'transparent_seed_segmentation'
        }

    def _create_visualization(self, image_bgra: np.ndarray, alpha_mask: np.ndarray,
                             components: List[Dict]) -> Optional[np.ndarray]:
        """
        创建显示分割结果的可视化图像

        Args:
            image_bgra: 原始BGRA图像
            alpha_mask: alpha掩膜
            components: 组件列表

        Returns:
            可视化图像
        """
        try:
            # 创建可视化图像的副本
            vis_image = image_bgra.copy()

            # 在透明区域添加白色背景以便查看
            transparent_pixels = vis_image[:, :, 3] == 0
            vis_image[transparent_pixels] = [255, 255, 255, 255]  # 白色背景

            # 绘制种子边界框
            for i, component in enumerate(components, 1):
                x, y, w, h = component['bbox']

                # 绘制边界框（绿色）
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0, 255), 2)

                # 添加种子ID标签
                label = f"S{i}"
                label_pos = (x, y - 5 if y > 20 else y + h + 20)
                cv2.putText(vis_image, label, label_pos,
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0, 255), 2)

                # 添加中心点
                center_x, center_y = int(component['centroid'][0]), int(component['centroid'][1])
                cv2.circle(vis_image, (center_x, center_y), 3, (255, 0, 0, 255), -1)

            return vis_image

        except Exception as e:
            self.logger.error(f"创建可视化图像时出错: {e}")
            return None

    def get_default_config(self) -> Dict:
        """获取默认配置"""
        return self.default_config.copy()
