#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Enhanced Segmentation GUI
- Test Chinese localization
- Test file management and automatic pairing
- Test various file naming conventions
- Validate GUI functionality
"""

import os
import sys
import cv2
import numpy as np
import tempfile
import shutil
from pathlib import Path

def create_test_environment():
    """创建测试环境和文件"""
    print("创建测试环境...")
    
    # 创建临时测试目录
    test_dir = tempfile.mkdtemp(prefix="gui_test_")
    print(f"测试目录: {test_dir}")
    
    # 创建测试图像
    test_images = []
    test_masks = []
    
    # 测试不同的命名约定
    naming_patterns = [
        ("seed001.jpg", "seed001_mask.png"),
        ("seed002.jpg", "seed002mask.png"),
        ("seed003.jpg", "seed003.png"),  # 掩膜文件没有特殊后缀
        ("sample_A.jpg", "sample_A_mask.jpg"),
        ("test图像.png", "test图像_掩膜.png"),  # 中文文件名
        ("data_001.bmp", "data_001_m.bmp"),
        ("image_final.tiff", "image_final_mask.tiff"),
    ]
    
    for i, (img_name, mask_name) in enumerate(naming_patterns):
        # 创建原始图像
        img_path = os.path.join(test_dir, img_name)
        original_image = create_test_original_image(400, 300, i)
        cv2.imwrite(img_path, original_image)
        test_images.append(img_path)
        
        # 创建对应的掩膜图像
        mask_path = os.path.join(test_dir, mask_name)
        mask_image = create_test_mask_image(400, 300, i)
        cv2.imwrite(mask_path, mask_image)
        test_masks.append(mask_path)
    
    # 创建一些没有配对的文件
    orphan_image = os.path.join(test_dir, "orphan_image.jpg")
    cv2.imwrite(orphan_image, create_test_original_image(200, 200, 99))
    test_images.append(orphan_image)
    
    orphan_mask = os.path.join(test_dir, "orphan_mask.png")
    cv2.imwrite(orphan_mask, create_test_mask_image(200, 200, 99))
    test_masks.append(orphan_mask)
    
    return test_dir, test_images, test_masks

def create_test_original_image(width, height, seed):
    """创建测试原始图像"""
    np.random.seed(seed)
    
    # 创建彩色渐变背景
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 添加渐变
    for y in range(height):
        for x in range(width):
            image[y, x, 0] = int(255 * x / width)  # 红色渐变
            image[y, x, 1] = int(255 * y / height)  # 绿色渐变
            image[y, x, 2] = 128  # 固定蓝色
    
    # 添加一些随机噪声
    noise = np.random.randint(0, 50, (height, width, 3), dtype=np.uint8)
    image = cv2.add(image, noise)
    
    # 添加一些几何形状作为"种子"
    center_x, center_y = width // 2, height // 2
    
    # 椭圆
    cv2.ellipse(image, (center_x - 80, center_y - 50), (30, 45), 0, 0, 360, (255, 255, 0), -1)
    
    # 圆形
    cv2.circle(image, (center_x + 80, center_y), 25, (0, 255, 255), -1)
    
    # 矩形（可能是比例尺）
    cv2.rectangle(image, (center_x - 50, center_y + 80), (center_x + 50, center_y + 90), (255, 0, 255), -1)
    
    return image

def create_test_mask_image(width, height, seed):
    """创建测试掩膜图像（白底黑种子）"""
    # 创建白色背景
    mask = np.ones((height, width), dtype=np.uint8) * 255
    
    # 添加黑色种子区域
    center_x, center_y = width // 2, height // 2
    
    # 椭圆种子
    cv2.ellipse(mask, (center_x - 80, center_y - 50), (30, 45), 0, 0, 360, 0, -1)
    
    # 圆形种子
    cv2.circle(mask, (center_x + 80, center_y), 25, 0, -1)
    
    # 矩形（比例尺）
    cv2.rectangle(mask, (center_x - 50, center_y + 80), (center_x + 50, center_y + 90), 0, -1)
    
    return mask

def test_file_pairing():
    """测试文件配对功能"""
    print("\n测试文件配对功能...")
    
    # 导入GUI模块进行测试
    try:
        from enhanced_segmentation_gui import EnhancedSegmentationGUI
        import tkinter as tk
        
        # 创建测试环境
        test_dir, test_images, test_masks = create_test_environment()
        
        # 创建GUI实例（不显示窗口）
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        gui = EnhancedSegmentationGUI(root)
        
        # 设置测试目录
        gui.current_folder = test_dir
        gui.method_var.set("mask")
        
        # 扫描文件
        gui.scan_folder_for_images()
        print(f"扫描到图像文件: {len(gui.image_files)}")
        print(f"扫描到掩膜文件: {len(gui.mask_files)}")
        
        # 测试自动配对
        gui.auto_pair_files()
        print(f"成功配对: {len(gui.paired_files)}")
        print(f"未配对文件: {len(gui.unpaired_files)}")
        
        # 显示配对结果
        print("\n配对结果:")
        for img_file, mask_file in gui.paired_files.items():
            img_name = os.path.basename(img_file)
            mask_name = os.path.basename(mask_file)
            print(f"  ✓ {img_name} ← {mask_name}")
        
        if gui.unpaired_files:
            print("\n未配对文件:")
            for unpaired in gui.unpaired_files:
                print(f"  ✗ {os.path.basename(unpaired)}")
        
        # 测试基础名称提取
        print("\n测试基础名称提取:")
        test_names = [
            "seed001.jpg",
            "seed001_mask.png",
            "sample_mask.jpg",
            "test图像_掩膜.png",
            "data_m.bmp"
        ]
        
        for name in test_names:
            base = gui.extract_base_name(name)
            print(f"  {name} → {base}")
        
        # 清理
        root.destroy()
        shutil.rmtree(test_dir)
        
        print("\n✅ 文件配对测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 文件配对测试失败: {e}")
        return False

def test_chinese_interface():
    """测试中文界面"""
    print("\n测试中文界面...")
    
    try:
        from enhanced_segmentation_gui import EnhancedSegmentationGUI
        import tkinter as tk
        
        # 创建GUI实例
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口进行测试
        
        gui = EnhancedSegmentationGUI(root)
        
        # 检查中文文本
        chinese_texts = [
            gui.root.title(),  # 窗口标题
            gui.method_desc_var.get(),  # 方法描述
            gui.status_var.get(),  # 状态信息
        ]
        
        print("界面文本检查:")
        for text in chinese_texts:
            if text:
                # 检查是否包含中文字符
                has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
                status = "✓" if has_chinese else "✗"
                print(f"  {status} {text}")
        
        root.destroy()
        
        print("✅ 中文界面测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 中文界面测试失败: {e}")
        return False

def test_file_management():
    """测试文件管理功能"""
    print("\n测试文件管理功能...")
    
    try:
        from enhanced_segmentation_gui import EnhancedSegmentationGUI
        import tkinter as tk
        
        # 创建测试环境
        test_dir, test_images, test_masks = create_test_environment()
        
        # 创建GUI实例
        root = tk.Tk()
        root.withdraw()
        
        gui = EnhancedSegmentationGUI(root)
        
        # 测试文件夹扫描
        gui.current_folder = test_dir
        gui.scan_folder_for_images()
        
        print(f"支持的格式: {gui.supported_formats}")
        print(f"扫描结果: {len(gui.image_files)} 图像, {len(gui.mask_files)} 掩膜")
        
        # 测试文件统计更新
        gui.method_var.set("sam")
        gui.update_file_stats()
        sam_stats = gui.file_stats_var.get()
        
        gui.method_var.set("mask")
        gui.update_file_stats()
        mask_stats = gui.file_stats_var.get()
        
        print(f"SAM模式统计: {sam_stats}")
        print(f"掩膜模式统计: {mask_stats}")
        
        # 测试文件列表显示
        gui.update_file_display()
        
        # 清理
        root.destroy()
        shutil.rmtree(test_dir)
        
        print("✅ 文件管理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 文件管理测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始增强GUI测试...")
    print("=" * 50)
    
    tests = [
        ("中文界面", test_chinese_interface),
        ("文件管理", test_file_management),
        ("文件配对", test_file_pairing),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 总计: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print(f"\n💥 {failed} 个测试失败！")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
