#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU-accelerated SAM Everything Command Line Interface
- Device selection (CPU/GPU)
- CUDA 11.2 compatibility
- YOLO label generation
- Batch processing support
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from sam_everything_gpu import GPUAcceleratedSAMEverything

def setup_logging(verbose: bool = False):
    """设置日志记录"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('sam_gpu.log', encoding='utf-8')
        ]
    )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='GPU-accelerated SAM Everything with YOLO label generation',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process single image with GPU
  python sam_gpu_cli.py --device gpu --input image.jpg --output ./results

  # Batch process folder with CPU
  python sam_gpu_cli.py --device cpu --input ./images --output ./results --batch

  # Use custom parameters
  python sam_gpu_cli.py --device gpu --input image.jpg --output ./results --points-per-side 64 --min-area 200

  # Disable YOLO label generation
  python sam_gpu_cli.py --device gpu --input image.jpg --output ./results --no-yolo-labels
        """
    )
    
    # 基本参数
    parser.add_argument('--device', choices=['cpu', 'gpu', 'cuda', 'auto'], default='auto',
                       help='Device to use for processing (default: auto)')
    parser.add_argument('--input', '-i', required=True,
                       help='Input image file or directory')
    parser.add_argument('--output', '-o', default='./output',
                       help='Output directory (default: ./output)')
    parser.add_argument('--model', default='sam_vit_h_4b8939.pth',
                       help='Path to SAM model file (default: sam_vit_h_4b8939.pth)')
    
    # 处理模式
    parser.add_argument('--batch', action='store_true',
                       help='Batch process all images in input directory')
    parser.add_argument('--extensions', nargs='+', 
                       default=['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],
                       help='Image file extensions to process (default: .jpg .jpeg .png .bmp .tiff)')
    
    # SAM参数 (官方demo默认值)
    parser.add_argument('--points-per-side', type=int, default=32,
                       help='Number of points per side for mask generation (default: 32)')
    parser.add_argument('--pred-iou-thresh', type=float, default=0.88,
                       help='Prediction IoU threshold (default: 0.88)')
    parser.add_argument('--stability-score-thresh', type=float, default=0.95,
                       help='Stability score threshold (default: 0.95)')
    parser.add_argument('--min-area', type=int, default=100,
                       help='Minimum object area in pixels (default: 100)')
    parser.add_argument('--overlap-threshold', type=float, default=0.1,
                       help='Mask overlap filtering threshold (default: 0.1)')
    parser.add_argument('--max-mask-ratio', type=float, default=0.8,
                       help='Maximum mask area ratio to filter whole-image masks (default: 0.8)')

    # 对象提取参数
    parser.add_argument('--no-mask-extraction', action='store_true',
                       help='Disable mask-based extraction (use rectangular cropping instead)')

    # YOLO标签参数
    parser.add_argument('--no-yolo-labels', action='store_true',
                       help='Disable YOLO label generation')
    parser.add_argument('--class-id', type=int, default=0,
                       help='Default class ID for YOLO labels (default: 0)')

    # 可视化参数
    parser.add_argument('--no-masks', action='store_true',
                       help='Disable mask visualization')
    parser.add_argument('--mask-alpha', type=float, default=0.35,
                       help='Mask transparency (0.0-1.0, default: 0.35)')
    
    # 其他选项
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--check-gpu', action='store_true',
                       help='Check GPU availability and exit')
    
    return parser.parse_args()

def check_gpu_availability():
    """检查GPU可用性"""
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"CUDA version: {torch.version.cuda}")
            print(f"GPU count: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
                print(f"  Memory: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
        else:
            print("No CUDA GPUs available")
            
    except ImportError:
        print("PyTorch not installed")
        return False
    
    return torch.cuda.is_available() if 'torch' in locals() else False

def create_config(args):
    """根据命令行参数创建配置"""
    return {
        'checkpoint_path': args.model,
        'device': args.device,

        # SAM参数
        'points_per_side': args.points_per_side,
        'pred_iou_thresh': args.pred_iou_thresh,
        'stability_score_thresh': args.stability_score_thresh,
        'min_object_area': args.min_area,
        'overlap_threshold': args.overlap_threshold,
        'max_mask_ratio': args.max_mask_ratio,

        # 对象提取参数
        'use_mask_extraction': not args.no_mask_extraction,

        # 可视化参数
        'show_masks': not args.no_masks,
        'mask_alpha': args.mask_alpha,

        # YOLO参数
        'generate_yolo_labels': not args.no_yolo_labels,
        'default_class_id': args.class_id,
    }

def process_single_image(sam_everything, input_path, output_dir, logger):
    """处理单个图像"""
    logger.info(f"处理图像: {input_path}")
    
    # 读取图像
    image = sam_everything.safe_imread(input_path)
    if image is None:
        logger.error(f"无法读取图像: {input_path}")
        return False
    
    # 创建输出目录
    base_name = Path(input_path).stem
    image_output_dir = os.path.join(output_dir, base_name)
    
    # 处理图像
    result = sam_everything.process_image(image, input_path, image_output_dir)
    
    if result['success']:
        logger.info(f"处理成功!")
        logger.info(f"  检测对象: {result['objects_count']}")
        logger.info(f"  处理时间: {result['processing_time']:.2f}秒")
        logger.info(f"  输出目录: {result['output_dir']}")
        if result.get('yolo_label_file'):
            logger.info(f"  YOLO标签: {result['yolo_label_file']}")
        return True
    else:
        logger.error(f"处理失败: {result.get('error', '未知错误')}")
        return False

def process_batch(sam_everything, input_dir, output_dir, extensions, logger):
    """批量处理图像"""
    logger.info(f"批量处理目录: {input_dir}")
    
    result = sam_everything.batch_process(input_dir, output_dir, extensions)
    
    if result['success']:
        logger.info(f"批量处理完成!")
        logger.info(f"  处理文件: {result['processed_count']}/{result['total_files']}")
        logger.info(f"  总对象数: {result['total_objects']}")
        logger.info(f"  输出目录: {output_dir}")
        return True
    else:
        logger.error(f"批量处理失败: {result.get('error', '未知错误')}")
        return False

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    # 检查GPU可用性
    if args.check_gpu:
        check_gpu_availability()
        return 0
    
    # 检查输入文件/目录
    if not os.path.exists(args.input):
        logger.error(f"输入路径不存在: {args.input}")
        return 1
    
    # 检查模型文件
    if not os.path.exists(args.model):
        logger.error(f"SAM模型文件不存在: {args.model}")
        logger.info("请下载SAM模型文件 sam_vit_h_4b8939.pth")
        return 1
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 创建配置
    config = create_config(args)
    
    # 初始化SAM Everything
    logger.info("初始化GPU-accelerated SAM Everything...")
    sam_everything = GPUAcceleratedSAMEverything(config)
    
    if sam_everything.sam_generator is None:
        logger.error("SAM Everything初始化失败")
        return 1
    
    # 处理图像
    start_time = time.time()
    
    if args.batch or os.path.isdir(args.input):
        # 批量处理
        success = process_batch(sam_everything, args.input, args.output, args.extensions, logger)
    else:
        # 单个文件处理
        success = process_single_image(sam_everything, args.input, args.output, logger)
    
    total_time = time.time() - start_time
    logger.info(f"总处理时间: {total_time:.2f}秒")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
