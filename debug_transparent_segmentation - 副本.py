#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
透明背景分割调试工具
帮助诊断"未找到有效种子组件"问题
"""

import os
import sys
import cv2
import numpy as np
import tempfile
from PIL import Image
import matplotlib.pyplot as plt

def analyze_image_properties(image_path):
    """分析图像属性"""
    print(f"\n🔍 分析图像: {image_path}")
    print("-" * 50)
    
    if not os.path.exists(image_path):
        print("❌ 文件不存在")
        return None
    
    try:
        # 使用PIL读取
        pil_image = Image.open(image_path)
        print(f"PIL模式: {pil_image.mode}")
        print(f"图像尺寸: {pil_image.size}")
        
        # 转换为numpy数组
        if pil_image.mode == 'RGBA':
            image_array = np.array(pil_image)
            print(f"数组形状: {image_array.shape}")
            
            # 分析alpha通道
            alpha_channel = image_array[:, :, 3]
            unique_alpha = np.unique(alpha_channel)
            print(f"Alpha通道唯一值: {unique_alpha}")
            print(f"Alpha通道范围: {alpha_channel.min()} - {alpha_channel.max()}")
            
            # 统计透明度
            transparent_pixels = np.sum(alpha_channel == 0)
            semi_transparent = np.sum((alpha_channel > 0) & (alpha_channel < 255))
            opaque_pixels = np.sum(alpha_channel == 255)
            total_pixels = alpha_channel.size
            
            print(f"完全透明像素: {transparent_pixels} ({transparent_pixels/total_pixels*100:.1f}%)")
            print(f"半透明像素: {semi_transparent} ({semi_transparent/total_pixels*100:.1f}%)")
            print(f"不透明像素: {opaque_pixels} ({opaque_pixels/total_pixels*100:.1f}%)")
            
            return image_array
        else:
            print("⚠️ 图像不是RGBA格式，可能没有透明度信息")
            return None
            
    except Exception as e:
        print(f"❌ 读取图像失败: {e}")
        return None

def test_alpha_processing(image_array, alpha_threshold=128):
    """测试alpha通道处理"""
    print(f"\n🧪 测试Alpha处理 (阈值: {alpha_threshold})")
    print("-" * 30)
    
    if image_array is None or len(image_array.shape) != 3 or image_array.shape[2] != 4:
        print("❌ 无效的RGBA图像")
        return None
    
    # 转换为BGRA
    image_bgra = cv2.cvtColor(image_array, cv2.COLOR_RGBA2BGRA)
    
    # 提取alpha通道
    alpha_channel = image_bgra[:, :, 3]
    print(f"Alpha通道统计:")
    print(f"  最小值: {alpha_channel.min()}")
    print(f"  最大值: {alpha_channel.max()}")
    print(f"  平均值: {alpha_channel.mean():.2f}")
    
    # 应用阈值
    _, alpha_mask = cv2.threshold(alpha_channel, alpha_threshold, 255, cv2.THRESH_BINARY)
    
    non_transparent_pixels = np.sum(alpha_mask > 0)
    print(f"阈值处理后非透明像素: {non_transparent_pixels}")
    
    if non_transparent_pixels == 0:
        print("❌ 阈值处理后没有非透明像素，尝试降低alpha_threshold")
        return None
    
    return alpha_mask

def test_connected_components(alpha_mask, min_area=100, max_area=100000):
    """测试连通组件分析"""
    print(f"\n🔗 测试连通组件分析")
    print(f"面积范围: {min_area} - {max_area}")
    print("-" * 30)
    
    if alpha_mask is None:
        print("❌ 无效的alpha掩膜")
        return []
    
    try:
        # 使用兼容的连通组件函数
        from transparent_seed_segmentation import safe_connected_components_with_stats
        
        num_labels, labels, stats, centroids = safe_connected_components_with_stats(alpha_mask, 8)
        
        print(f"找到 {num_labels-1} 个连通组件")
        
        valid_components = []
        for i in range(1, num_labels):  # 跳过背景标签0
            area = stats[i, cv2.CC_STAT_AREA]
            x = stats[i, cv2.CC_STAT_LEFT]
            y = stats[i, cv2.CC_STAT_TOP]
            w = stats[i, cv2.CC_STAT_WIDTH]
            h = stats[i, cv2.CC_STAT_HEIGHT]
            
            print(f"组件 {i}: 面积={area}, 位置=({x},{y}), 尺寸={w}x{h}")
            
            if min_area <= area <= max_area:
                valid_components.append({
                    'label': i,
                    'area': area,
                    'bbox': (x, y, w, h)
                })
                print(f"  ✅ 有效组件")
            else:
                print(f"  ❌ 面积超出范围 ({min_area}-{max_area})")
        
        print(f"\n有效组件数量: {len(valid_components)}")
        return valid_components
        
    except Exception as e:
        print(f"❌ 连通组件分析失败: {e}")
        return []

def suggest_parameters(image_array, alpha_mask, components):
    """建议参数调整"""
    print(f"\n💡 参数建议")
    print("-" * 20)
    
    if image_array is None:
        print("无法分析，图像无效")
        return
    
    # 分析alpha通道分布
    alpha_channel = image_array[:, :, 3]
    non_zero_alpha = alpha_channel[alpha_channel > 0]
    
    if len(non_zero_alpha) > 0:
        suggested_threshold = max(1, int(non_zero_alpha.min()))
        print(f"建议alpha_threshold: {suggested_threshold} (当前最小非零alpha值)")
    
    # 分析组件大小
    if alpha_mask is not None:
        try:
            from transparent_seed_segmentation import safe_connected_components_with_stats
            num_labels, labels, stats, centroids = safe_connected_components_with_stats(alpha_mask, 8)
            
            if num_labels > 1:
                areas = [stats[i, cv2.CC_STAT_AREA] for i in range(1, num_labels)]
                min_found_area = min(areas)
                max_found_area = max(areas)
                
                print(f"实际组件面积范围: {min_found_area} - {max_found_area}")
                print(f"建议min_seed_area: {max(1, min_found_area - 50)}")
                print(f"建议max_seed_area: {max_found_area + 10000}")
        except:
            pass
    
    # 其他建议
    print(f"\n其他建议:")
    print(f"- 如果图像很小，降低min_seed_area到50或更低")
    print(f"- 如果种子很大，增加max_seed_area到200000或更高")
    print(f"- 尝试禁用噪声移除: remove_noise=False")
    print(f"- 检查图像是否真的有透明背景")

def create_debug_visualization(image_array, alpha_mask, components, output_path):
    """创建调试可视化图像"""
    print(f"\n📊 创建调试可视化")
    print("-" * 20)
    
    try:
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 原始图像
        if image_array is not None:
            axes[0, 0].imshow(image_array)
            axes[0, 0].set_title('原始RGBA图像')
            axes[0, 0].axis('off')
            
            # Alpha通道
            axes[0, 1].imshow(image_array[:, :, 3], cmap='gray')
            axes[0, 1].set_title('Alpha通道')
            axes[0, 1].axis('off')
        
        # Alpha掩膜
        if alpha_mask is not None:
            axes[1, 0].imshow(alpha_mask, cmap='gray')
            axes[1, 0].set_title('Alpha掩膜 (阈值处理后)')
            axes[1, 0].axis('off')
            
            # 组件标记
            if components:
                display_image = cv2.cvtColor(alpha_mask, cv2.COLOR_GRAY2RGB)
                for i, comp in enumerate(components):
                    x, y, w, h = comp['bbox']
                    cv2.rectangle(display_image, (x, y), (x+w, y+h), (255, 0, 0), 2)
                    cv2.putText(display_image, f"{i+1}", (x, y-5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
                
                axes[1, 1].imshow(display_image)
                axes[1, 1].set_title(f'检测到的组件 ({len(components)})')
            else:
                axes[1, 1].text(0.5, 0.5, '未检测到有效组件', 
                               ha='center', va='center', transform=axes[1, 1].transAxes)
                axes[1, 1].set_title('检测结果')
            axes[1, 1].axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 调试图像已保存: {output_path}")
        
    except Exception as e:
        print(f"❌ 创建可视化失败: {e}")

def debug_image(image_path, alpha_threshold=128, min_area=100, max_area=100000):
    """调试单张图像"""
    print(f"🐛 调试透明背景分割")
    print("=" * 60)
    
    # 分析图像属性
    image_array = analyze_image_properties(image_path)
    
    # 测试alpha处理
    alpha_mask = test_alpha_processing(image_array, alpha_threshold)
    
    # 测试连通组件
    components = test_connected_components(alpha_mask, min_area, max_area)
    
    # 参数建议
    suggest_parameters(image_array, alpha_mask, components)
    
    # 创建可视化
    debug_output = f"debug_{os.path.splitext(os.path.basename(image_path))[0]}.png"
    create_debug_visualization(image_array, alpha_mask, components, debug_output)
    
    # 总结
    print(f"\n📋 调试总结")
    print("-" * 20)
    if len(components) > 0:
        print(f"✅ 找到 {len(components)} 个有效种子组件")
        print("建议使用当前参数进行处理")
    else:
        print("❌ 未找到有效种子组件")
        print("请根据上述建议调整参数")
    
    return components

def create_test_transparent_image():
    """创建测试用的透明背景图像"""
    print("\n🎨 创建测试透明背景图像")
    
    # 创建RGBA图像
    image = np.zeros((300, 400, 4), dtype=np.uint8)
    
    # 添加几个种子（不透明区域）
    seeds = [
        {'center': (100, 100), 'radius': 30, 'color': [180, 100, 50, 255]},
        {'center': (250, 150), 'radius': 25, 'color': [50, 180, 100, 255]},
        {'center': (150, 220), 'radius': 20, 'color': [100, 50, 180, 255]},
    ]
    
    for seed in seeds:
        cv2.circle(image, seed['center'], seed['radius'], seed['color'], -1)
    
    # 保存测试图像
    test_path = "debug_test_transparent.png"
    pil_image = Image.fromarray(image, 'RGBA')
    pil_image.save(test_path, 'PNG')
    
    print(f"✅ 测试图像已创建: {test_path}")
    return test_path

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python debug_transparent_segmentation.py <图像路径>")
        print("  python debug_transparent_segmentation.py --create-test")
        print("\n示例:")
        print("  python debug_transparent_segmentation.py seeds.png")
        print("  python debug_transparent_segmentation.py --create-test")
        return 1
    
    if sys.argv[1] == "--create-test":
        # 创建测试图像并调试
        test_path = create_test_transparent_image()
        debug_image(test_path)
    else:
        # 调试指定图像
        image_path = sys.argv[1]
        
        # 可选参数
        alpha_threshold = int(sys.argv[2]) if len(sys.argv) > 2 else 128
        min_area = int(sys.argv[3]) if len(sys.argv) > 3 else 100
        max_area = int(sys.argv[4]) if len(sys.argv) > 4 else 100000
        
        debug_image(image_path, alpha_threshold, min_area, max_area)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
