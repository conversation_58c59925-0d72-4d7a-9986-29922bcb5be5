# GPU-accelerated SAM Everything 故障排除指南

## 🔧 常见问题及解决方案

### 1. tkinter Scale 组件错误

**错误信息:**
```
_tkinter.TclError: unknown option "-resolution"
```

**原因:** `ttk.Scale` 不支持 `resolution` 参数，只有 `tk.Scale` 支持。

**解决方案:**
使用修复版本的GUI：
```bash
python sam_everything_gpu_gui_fixed.py
```

或者手动修复原文件中的Scale组件：
```python
# 错误的写法 (ttk.Scale with resolution)
ttk.Scale(parent, from_=0.5, to=1.0, variable=var, 
          orient=tk.HORIZONTAL, resolution=0.01)

# 正确的写法 (tk.Scale with resolution)
tk.Scale(parent, from_=0.5, to=1.0, variable=var, 
         orient=tk.HORIZONTAL, resolution=0.01)
```

### 2. GPU 不可用错误

**错误信息:**
```
CUDA out of memory
RuntimeError: No CUDA GPUs are available
```

**解决方案:**
1. 检查CUDA安装：
   ```bash
   nvidia-smi
   python -c "import torch; print(torch.cuda.is_available())"
   ```

2. 强制使用CPU：
   ```bash
   python sam_gpu_cli.py --device cpu --input image.jpg --output results
   ```

3. 降低GPU内存使用：
   ```bash
   python sam_gpu_cli.py --device gpu --points-per-side 16 --min-area 200
   ```

### 3. 模型文件缺失

**错误信息:**
```
FileNotFoundError: SAM模型文件不存在: sam_vit_h_4b8939.pth
```

**解决方案:**
下载SAM模型文件：
```bash
# 使用wget
wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

# 使用curl
curl -L -o sam_vit_h_4b8939.pth https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

# 使用Python下载
python -c "
import urllib.request
url = 'https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth'
urllib.request.urlretrieve(url, 'sam_vit_h_4b8939.pth')
print('模型下载完成')
"
```

### 4. 依赖包安装问题

**错误信息:**
```
ImportError: No module named 'segment_anything'
ImportError: No module named 'torch'
```

**解决方案:**
```bash
# 安装完整依赖
pip install -r requirements.txt

# 或手动安装
pip install torch==1.12.1+cu112 torchvision==0.13.1+cu112 --extra-index-url https://download.pytorch.org/whl/cu112
pip install segment-anything opencv-python pillow numpy
```

### 5. 中文路径问题

**错误信息:**
```
cv2.error: OpenCV(4.x.x) error: can't read image
```

**解决方案:**
使用中文路径修复工具：
```bash
python quick_fix_chinese_path.py
```

### 6. YOLO标签文件未生成

**问题:** 处理完成但没有生成YOLO标签文件

**解决方案:**
1. 检查YOLO标签生成是否启用：
   ```bash
   python sam_gpu_cli.py --input image.jpg --output results  # 默认启用
   ```

2. 手动启用YOLO标签生成：
   ```bash
   python sam_gpu_cli.py --input image.jpg --output results --class-id 0
   ```

3. 检查输出目录中的 `yolo_label` 文件夹

### 7. 性能优化

**问题:** 处理速度太慢

**解决方案:**
1. 使用GPU加速：
   ```bash
   python sam_gpu_cli.py --device gpu
   ```

2. 调整参数以提高速度：
   ```bash
   python sam_gpu_cli.py --device gpu --points-per-side 16 --min-area 500
   ```

3. 降低图像分辨率后再处理

## 🧪 测试和验证

### 运行安装测试
```bash
python test_installation.py
```

### 测试GUI组件
```bash
python test_gui_simple.py
```

### 测试命令行功能
```bash
# 检查GPU状态
python sam_gpu_cli.py --check-gpu

# 测试处理单个图像
python sam_gpu_cli.py --device auto --input test_image.jpg --output test_output --verbose
```

## 📞 获取帮助

如果以上解决方案都无法解决您的问题，请：

1. 运行完整的环境检查：
   ```bash
   python test_installation.py > test_results.txt 2>&1
   ```

2. 收集系统信息：
   ```bash
   python -c "
   import sys, torch, cv2, numpy as np
   print(f'Python: {sys.version}')
   print(f'PyTorch: {torch.__version__}')
   print(f'CUDA available: {torch.cuda.is_available()}')
   print(f'OpenCV: {cv2.__version__}')
   print(f'NumPy: {np.__version__}')
   "
   ```

3. 提供详细的错误信息和系统配置

## 🔄 版本兼容性

- **推荐使用:** `sam_everything_gpu_gui_fixed.py` (修复了tkinter兼容性问题)
- **原始版本:** `sam_everything_gpu_gui.py` (可能存在tkinter Scale问题)
- **备用版本:** `sam_everything_gui_simplified.py` (原始CPU版本)

选择适合您系统的版本使用。
