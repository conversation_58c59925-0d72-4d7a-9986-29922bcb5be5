# Mask-Based Segmentation Guide

## 🎯 Overview

The mask-based segmentation system processes pre-made mask images where seeds appear as **black regions on white/light backgrounds**. This approach provides pixel-perfect accuracy for seed extraction when you already have high-quality mask images.

## 🆚 SAM vs Mask-Based Comparison

| Feature | SAM (Automatic) | Mask-Based |
|---------|----------------|------------|
| **Input Required** | Original image only | Original + mask image |
| **Accuracy** | AI-based detection | Pixel-perfect (mask quality dependent) |
| **Speed** | Slower (AI processing) | Faster (direct processing) |
| **Preprocessing** | None required | Mask creation required |
| **Use Case** | General object detection | Precise seed extraction |
| **Scale Bar Detection** | Limited | Built-in detection |

## 📋 Requirements

### Input Images
- **Original Image**: High-quality seed photograph
- **Mask Image**: Binary mask where:
  - Seeds appear as **black regions** (RGB: 0,0,0)
  - Background is **white/light** (RGB: 255,255,255)
  - Same dimensions as original image (or will be resized automatically)

### Supported Formats
- **Input**: JPG, JPEG, PNG, BMP, TIFF
- **Output**: PNG (with transparency support) or JPG

## 🛠️ Installation & Setup

### Prerequisites
```bash
pip install opencv-python numpy pillow
```

### Files Required
- `mask_based_segmentation.py` - Core segmentation class
- `segmentation_cli.py` - Command line interface
- `unified_segmentation_gui.py` - Graphical interface

## 🚀 Usage Examples

### Command Line Interface

#### Single Image Processing
```bash
# Basic usage
python segmentation_cli.py --method mask --mask-image seed_mask.png --input seed_original.jpg --output results

# With custom parameters
python segmentation_cli.py --method mask \
    --mask-image seed_mask.png \
    --input seed_original.jpg \
    --output results \
    --min-seed-area 200 \
    --max-seed-area 30000 \
    --padding 15
```

#### Batch Processing
```bash
# Process multiple image pairs
python segmentation_cli.py --method mask \
    --mask-dir ./masks \
    --input-dir ./originals \
    --output ./results \
    --batch
```

### Graphical User Interface
```bash
# Launch unified GUI
python unified_segmentation_gui.py
```

1. Select "Mask-based" method
2. Click "Select Images" to choose original images
3. Click "Select Masks" to choose mask images
4. Adjust parameters as needed
5. Click "Process Current" or "Batch Process All"

### Python API
```python
from mask_based_segmentation import MaskBasedSegmentation

# Configure segmentation
config = {
    'min_seed_area': 100,        # Minimum seed area (pixels)
    'max_seed_area': 50000,      # Maximum seed area (pixels)
    'padding': 10,               # Padding around crops (pixels)
    'binary_threshold': 127,     # Binary threshold (0-255)
    'invert_mask': True,         # True for black seeds on white
    'output_format': 'png',      # Output format
    'include_scale_bars': True,  # Detect scale bars
}

# Initialize system
segmentation = MaskBasedSegmentation(config)

# Process single image pair
result = segmentation.process_mask_and_original(
    mask_path="seed_mask.png",
    original_path="seed_original.jpg",
    output_dir="results"
)

# Check results
if result['success']:
    print(f"Seeds extracted: {result['seeds_count']}")
    print(f"Scale bars detected: {result['scale_bars_count']}")
    print(f"Processing time: {result['processing_time']:.2f}s")
    
    # Access individual seed information
    for seed in result['seeds']:
        print(f"Seed {seed['seed_id']}: {seed['filename']}")
        print(f"  Size: {seed['size']}")
        print(f"  Area: {seed['area']} pixels")
else:
    print(f"Processing failed: {result['error']}")
```

## ⚙️ Configuration Parameters

### Core Parameters
- **`min_seed_area`** (default: 100): Minimum area for valid seeds (pixels)
- **`max_seed_area`** (default: 50000): Maximum area for valid seeds (pixels)
- **`padding`** (default: 10): Padding around cropped seeds (pixels)
- **`binary_threshold`** (default: 127): Threshold for binary conversion (0-255)
- **`invert_mask`** (default: True): Whether to invert mask colors

### Advanced Parameters
- **`connectivity`** (default: 8): Connectivity for connected components (4 or 8)
- **`output_format`** (default: 'png'): Output format ('png' or 'jpg')
- **`include_scale_bars`** (default: True): Enable scale bar detection
- **`scale_bar_min_aspect`** (default: 5.0): Minimum aspect ratio for scale bars
- **`scale_bar_max_area_ratio`** (default: 0.1): Maximum area ratio for scale bars

## 📊 Output Structure

```
output/
├── image1/
│   ├── image1_seed_001.png          # Individual seed crops
│   ├── image1_seed_002.png
│   ├── image1_seed_003.png
│   └── image1_mask_segmentation.jpg # Visualization with bounding boxes
├── image2/
│   ├── image2_seed_001.png
│   └── image2_mask_segmentation.jpg
```

### Result Information
Each processing operation returns detailed information:
```python
{
    'success': True,
    'seeds_count': 5,                    # Number of seeds extracted
    'scale_bars_count': 1,               # Number of scale bars detected
    'processing_time': 0.45,             # Processing time in seconds
    'output_dir': 'results/image1',      # Output directory
    'visualization_path': 'results/image1/image1_mask_segmentation.jpg',
    'seeds': [                           # Individual seed information
        {
            'seed_id': 1,
            'filename': 'image1_seed_001.png',
            'path': 'results/image1/image1_seed_001.png',
            'size': (120, 85),           # Width, height
            'area': 3420,                # Area in pixels
            'bbox': (45, 67, 120, 85),   # Bounding box (x, y, w, h)
            'centroid': (105.2, 109.8),  # Center coordinates
            'aspect_ratio': 1.41,        # Width/height ratio
            'format': 'png'
        },
        # ... more seeds
    ],
    'method': 'mask_based_segmentation'
}
```

## 🎨 Creating Quality Mask Images

### Manual Creation (Recommended for Precision)
1. **Use image editing software** (Photoshop, GIMP, etc.)
2. **Create binary mask**:
   - Seeds: Pure black (RGB: 0,0,0)
   - Background: Pure white (RGB: 255,255,255)
3. **Ensure pixel-perfect boundaries** around seeds
4. **Save as PNG** to preserve quality

### Semi-Automatic Creation
1. **Use existing segmentation tools** to create initial masks
2. **Manually refine** boundaries for precision
3. **Convert to binary** (black seeds on white)

### Quality Guidelines
- ✅ **High contrast**: Pure black seeds on pure white background
- ✅ **Clean boundaries**: No gray pixels at edges
- ✅ **Complete coverage**: Seeds fully filled, no holes
- ✅ **Accurate shapes**: Mask matches actual seed boundaries
- ❌ **Avoid**: Grayscale values, partial transparency, noise

## 🔧 Troubleshooting

### Common Issues

#### No Seeds Detected
**Symptoms**: `seeds_count: 0` in results
**Solutions**:
- Check mask colors (should be black seeds on white background)
- Verify `invert_mask` setting matches your mask format
- Adjust `min_seed_area` if seeds are smaller than expected
- Check `binary_threshold` value

#### Too Many/Few Seeds
**Symptoms**: Unexpected number of detected seeds
**Solutions**:
- Adjust `min_seed_area` and `max_seed_area` parameters
- Clean up mask image to remove noise
- Check for connected seed regions that should be separate

#### Poor Crop Quality
**Symptoms**: Cropped seeds have wrong boundaries
**Solutions**:
- Increase `padding` for more context around seeds
- Improve mask accuracy with better boundaries
- Ensure original and mask images are properly aligned

#### Scale Bars Not Detected
**Symptoms**: Scale bars treated as seeds
**Solutions**:
- Adjust `scale_bar_min_aspect` (lower for shorter scale bars)
- Adjust `scale_bar_max_area_ratio` (higher for larger scale bars)
- Set `include_scale_bars: False` to disable detection

### Error Messages

#### "Failed to load mask image"
- Check file path and permissions
- Verify image format is supported
- Ensure file is not corrupted

#### "Invalid mask image"
- Check image dimensions (must be > 10x10 pixels)
- Verify image format (should be readable by OpenCV)

#### "No valid seed components found"
- Check mask has black regions for seeds
- Verify `invert_mask` setting
- Adjust area thresholds

## 📈 Performance Tips

### For Best Speed
- Use smaller images when possible
- Set appropriate area thresholds to filter noise
- Disable scale bar detection if not needed
- Use JPG output format for smaller files

### For Best Quality
- Use high-resolution original images
- Create precise mask boundaries
- Use PNG output format for transparency
- Add appropriate padding around seeds

### Batch Processing
- Organize files with matching names (e.g., `seed001.jpg` + `seed001_mask.png`)
- Use consistent naming conventions
- Process in smaller batches for better memory management

## 🧪 Testing

### Run Tests
```bash
# Run all tests
python run_tests.py

# Create sample test images
python test_mask_based_segmentation.py create-samples

# Test with sample images
python segmentation_cli.py --method mask --mask-image sample_mask.png --input sample_original.jpg --output test_output
```

### Validate Results
1. **Check seed count**: Matches expected number
2. **Inspect crops**: Seeds properly extracted with good boundaries
3. **Verify scale bars**: Correctly identified and separated
4. **Review visualization**: Bounding boxes align with actual seeds
